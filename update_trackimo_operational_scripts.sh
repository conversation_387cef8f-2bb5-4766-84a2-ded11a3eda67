#!/bin/bash

# Count the number of running fota scripts
count=$(pgrep -f 'fota.*python3 -u python_scripts/fota_scripts/' | wc -l)

# Check if any scripts were found
if [ "$count" -gt 0 ]; then
    echo "Found $count running fota script(s)" >&2
    exit 0
fi

# go to repo folder
cd /home/<USER>/trackimo_operational_scripts || {
    echo "Failed to find operational scripts repo folder" >&2
    exit 1
}

# pull repo
git clean -f -d  # Clean untracked files and directories
git reset --hard # remove any local changes
if ! git pull; then
    echo "Failed to pull from the repository" >&2
    exit 1
fi

# Ensure all .sh files in current folder (repo) have execute permissions
find . -type f -name "*.sh" -exec chmod +x {} \;

# If no new commits were found, exit silently
exit