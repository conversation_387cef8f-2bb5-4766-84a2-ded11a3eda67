import pymysql
import requests
import json
from datetime import datetime
import base64

# === Email Configuration ===
MANDRILL_API_KEY = '**********************'
SENDER = '<EMAIL>'
RECIPIENTS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','<EMAIL>']
SUBJECT = 'PetLoc8 Devices Firmware Analytics Dashboard'

# === Database Configuration ===
DB_HOST = 'loc8-aurora-instance-1.crqgu222g0za.eu-central-1.rds.amazonaws.com'
DB_USER = 'trackimo'
DB_PASSWORD = 'Daniel81395'
DB_NAME = 'trackimo'

# === Grafana Configuration ===
GRAFANA_SNAPSHOT_URL = 'https://grafana.loc8.local/dashboard/snapshot/TQ6FBNgqyVS2IFJQ50el47I7wTnkim6j'

# SQL Queries
FIRMWARE_QUERY = """
SELECT us_fw, bt_fw, COUNT(bt_fw) AS count
FROM (
    SELECT devices.id,
           RIGHT(devices_states.fw_version, 13) AS us_fw,
           device_additional_details.btFWVersion AS bt_fw
    FROM devices
    JOIN devices_states ON devices.id = devices_states.device_id
    JOIN device_additional_details ON device_additional_details.device_id = devices.id
    JOIN devices_last_location ON devices.id = devices_last_location.device_id
    WHERE devices_last_location.updated > NOW() - INTERVAL 30 DAY
      AND devices.brand = 24
    GROUP BY devices.id
) AS t2
GROUP BY us_fw, bt_fw
ORDER BY count DESC;
"""

TOTAL_ACTIVE_QUERY = """
SELECT COUNT(*) AS total_active
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
WHERE d.status = true
  AND d.account_id > 0
  AND d.user_id > 0
  AND ds.fw_version LIKE '%W217_VPET_E_M%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

COMMUNICATING_DEVICES_QUERY = """
SELECT COUNT(DISTINCT d.id) AS communicating_count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
JOIN devices_last_location dll ON d.id = dll.device_id
WHERE d.status = true 
AND d.account_id > 0
AND d.user_id > 0
AND dll.updated > NOW() - INTERVAL 30 DAY
AND ds.fw_version LIKE '%W217_VPET_E_M%'
AND dad.btFWVersion LIKE 'CAT1_BT%';
"""


def fetch_all_data():
    """Fetch all required data from database"""
    conn = pymysql.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        db=DB_NAME,
        cursorclass=pymysql.cursors.DictCursor
    )

    try:
        with conn.cursor() as cursor:
            # Get firmware data
            cursor.execute(FIRMWARE_QUERY)
            firmware_data = cursor.fetchall()

            # Get total active devices
            cursor.execute(TOTAL_ACTIVE_QUERY)
            total_active = cursor.fetchone()['total_active']

            # Get communicating devices count
            cursor.execute(COMMUNICATING_DEVICES_QUERY)
            communicating_count = cursor.fetchone()['communicating_count']

            return {
                'firmware_data': firmware_data,
                'total_active': total_active,
                'communicating_count': communicating_count
            }
    finally:
        conn.close()


def get_grafana_snapshot_image():
    """Get Grafana snapshot as PNG image for embedding"""
    try:
        # Convert snapshot URL to PNG format
        png_url = GRAFANA_SNAPSHOT_URL + ".png?width=1200&height=600"

        print(f"🖼️  Fetching Grafana snapshot from: {png_url}")

        # Fetch the image
        response = requests.get(png_url, timeout=30)

        if response.status_code == 200:
            # Convert to base64 for email embedding
            image_data = base64.b64encode(response.content).decode()
            print("✅ Grafana snapshot fetched successfully!")
            return f"data:image/png;base64,{image_data}"
        else:
            print(f"⚠️  Failed to fetch Grafana image: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ Error fetching Grafana snapshot: {str(e)}")
        return None


def create_modern_html_report(data, grafana_image=None):
    """Create a modern, responsive HTML report with Grafana integration"""
    firmware_data = data['firmware_data']
    total_active = data['total_active']
    communicating_count = data['communicating_count']

    # Calculate additional metrics
    total_firmware_entries = len(firmware_data)
    communication_rate = round((communicating_count / total_active * 100), 1) if total_active > 0 else 0

    # Generate firmware table rows
    firmware_rows = ""
    for i, row in enumerate(firmware_data):  # Show all firmware combinations
        gradient_opacity = max(0.1, 1 - (i * 0.02))  # Gentler gradient fade for more rows
        firmware_rows += f"""
        <tr style="background: linear-gradient(90deg, rgba(16, 185, 129, {gradient_opacity}) 0%, rgba(167, 243, 208, {gradient_opacity / 2}) 100%);">
            <td style="padding: 12px; font-weight: 500;">{row['us_fw'] or 'N/A'}</td>
            <td style="padding: 12px; font-weight: 500;">{row['bt_fw'] or 'N/A'}</td>
            <td style="padding: 12px; text-align: center;">
                <span style="background: #111827; color: white; padding: 4px 12px; border-radius: 20px; font-weight: bold;">
                    {row['count']}
                </span>
            </td>
        </tr>
        """

    # Create Grafana section if image is available
    grafana_section = ""
    if grafana_image:
        grafana_section = f"""
        <div class="grafana-section">
            <div class="table-header">
                <h2>📈 Real-time Monitoring Dashboard</h2>
                <p>Live system metrics and performance indicators
                    <span class="badge">Updated Continuously</span>
                </p>
            </div>

            <div class="grafana-container">
                <div style="text-align: center; margin-top: 15px;">
                    <a href="{GRAFANA_SNAPSHOT_URL}" target="_blank" 
                       style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%); 
                              color: white; padding: 12px 24px; border-radius: 25px; text-decoration: none; 
                              font-weight: 600; transition: transform 0.2s ease;">
                        🔗 View Interactive Dashboard
                    </a>
                </div>
            </div>
        </div>
        """
    else:
        # Fallback if image fails to load
        grafana_section = f"""
        <div class="grafana-section">
            <div class="table-header">
                <h2>📈 Real-time Monitoring Dashboard</h2>
                <p>Live system metrics and performance indicators</p>
            </div>

            <div style="text-align: center; padding: 40px; background: #f8fafc; border-radius: 12px; border: 2px dashed #cbd5e1;">
                <div style="font-size: 3rem; margin-bottom: 15px;">📊</div>
                <h3 style="color: #374151; margin-bottom: 10px;">Grafana Dashboard Available</h3>
                <p style="color: #6b7280; margin-bottom: 20px;">Click below to view the interactive monitoring dashboard</p>
                <a href="{GRAFANA_SNAPSHOT_URL}" target="_blank" 
                   style="display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%); 
                          color: white; padding: 12px 24px; border-radius: 25px; text-decoration: none; 
                          font-weight: 600;">
                    🔗 Open Grafana Dashboard
                </a>
            </div>
        </div>
        """

    # Create the HTML template
    html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PetLoc8 Firmware Analytics</title>
        <style>
            * {{
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }}

            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }}

            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 20px;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
                overflow: hidden;
            }}

            .header {{
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
                color: white;
                padding: 40px;
                text-align: center;
                position: relative;
                overflow: hidden;
            }}

            .header::before {{
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="3" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.08)"/></svg>');
                animation: float 20s infinite linear;
            }}

            @keyframes float {{
                0% {{ transform: rotate(0deg) translate(-50%, -50%); }}
                100% {{ transform: rotate(360deg) translate(-50%, -50%); }}
            }}

            .header h1 {{
                font-size: 2.5rem;
                margin-bottom: 10px;
                font-weight: 700;
                position: relative;
                z-index: 1;
            }}

            .header p {{
                font-size: 1.1rem;
                opacity: 0.9;
                position: relative;
                z-index: 1;
            }}

            .stats-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 30px;
                padding: 40px;
                background: #f8fafc;
            }}

            .stat-card {{
                background: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                position: relative;
                overflow: hidden;
            }}

            .stat-card::before {{
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ef4444, #10b981);
            }}

            .stat-card:hover {{
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            }}

            .stat-number {{
                font-size: 3rem;
                font-weight: 800;
                color: #1f2937;
                margin-bottom: 10px;
                display: block;
            }}

            .stat-label {{
                font-size: 1rem;
                color: #6b7280;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}

            .table-section {{
                padding: 40px;
            }}

            .table-header {{
                text-align: center;
                margin-bottom: 30px;
            }}

            .table-header h2 {{
                font-size: 2rem;
                color: #1f2937;
                margin-bottom: 10px;
            }}

            .table-header p {{
                color: #6b7280;
                font-size: 1.1rem;
            }}

            .modern-table {{
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }}

            .modern-table th {{
                background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
                color: white;
                padding: 20px;
                text-align: left;
                font-weight: 600;
                font-size: 1rem;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}

            .modern-table th:last-child {{
                text-align: center;
            }}

            .modern-table tr:hover {{
                transform: scale(1.01);
                transition: transform 0.2s ease;
            }}

            .grafana-container {{
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            }}

            .footer {{
                background: #f8fafc;
                padding: 30px;
                text-align: center;
                color: #6b7280;
                border-top: 1px solid #e5e7eb;
            }}

            .timestamp {{
                font-weight: 600;
                color: #374151;
            }}

            .badge {{
                display: inline-block;
                padding: 6px 12px;
                background: #dbeafe;
                color: #1d4ed8;
                border-radius: 20px;
                font-size: 0.85rem;
                font-weight: 600;
                margin-left: 10px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🐾 PetLoc8 Firmware Analytics</h1>
                <p>Comprehensive device firmware tracking and monitoring dashboard</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">{total_active:,}</span>
                    <div class="stat-label">Total Active Devices</div>
                </div>

                <div class="stat-card">
                    <span class="stat-number">{communicating_count:,}</span>
                    <div class="stat-label">Devices Communicating (30d)</div>
                </div>

                <div class="stat-card">
                    <span class="stat-number">{communication_rate}%</span>
                    <div class="stat-label">Communication Rate</div>
                </div>

                <div class="stat-card">
                    <span class="stat-number">{total_firmware_entries}</span>
                    <div class="stat-label">Firmware Combinations</div>
                </div>
            </div>

            {grafana_section}

            <div class="table-section">
                <div class="table-header">
                    <h2>📊 Firmware Distribution Analysis</h2>
                    <p>Complete breakdown of all firmware combinations across active devices
                        <span class="badge">Last 30 Days</span>
                    </p>
                </div>

                <table class="modern-table">
                    <thead>
                        <tr>
                            <th>Device Firmware</th>
                            <th>Bluetooth Firmware</th>
                            <th>Device Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        {firmware_rows}
                    </tbody>
                </table>
            </div>

            <div class="footer">
                <p>📅 Report generated on <span class="timestamp">{datetime.now().strftime('%B %d, %Y at %I:%M %p')}</span></p>
                <p style="margin-top: 10px; font-size: 0.9rem;">
                    Automated PetLoc8 Device Monitoring System | Trackimo Analytics
                </p>
            </div>
        </div>
    </body>
    </html>
    """

    return html


def send_enhanced_email(html_body):
    """Send the enhanced email with modern design"""
    to_list = [{"email": email, "type": "to"} for email in RECIPIENTS]

    payload = {
        "key": MANDRILL_API_KEY,
        "message": {
            "from_email": SENDER,
            "from_name": "PetLoc8 Analytics",
            "to": to_list,
            "subject": SUBJECT,
            "html": html_body,
            "important": True,
            "track_opens": True,
            "track_clicks": True,
            "auto_text": True,
            "preserve_recipients": False
        }
    }

    try:
        response = requests.post(
            "https://mandrillapp.com/api/1.0/messages/send.json",
            data=json.dumps(payload),
            headers={'Content-Type': 'application/json'}
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ Enhanced email sent successfully!")
            print(f"📧 Email status: {result[0].get('status', 'unknown')}")
            return True
        else:
            print(f"❌ Failed to send email: {response.status_code}")
            print(f"Error details: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Error sending email: {str(e)}")
        return False


def main():
    """Main execution function"""
    try:
        # Fetch all data
        print("📊 Fetching analytics data from database...")
        all_data = fetch_all_data()

        if not all_data['firmware_data']:
            print("⚠️  No firmware data found for the last 30 days.")
            return

        # Fetch Grafana snapshot
        print("📈 Fetching Grafana dashboard snapshot...")
        grafana_image = get_grafana_snapshot_image()

        # Display summary
        print(f"✅ Data retrieved successfully!")

        # Generate HTML report
        print("\n🎨 Generating modern HTML report with Grafana integration...")
        html_report = create_modern_html_report(all_data, grafana_image)

        # Send email
        print("📧 Sending enhanced email report...")
        if send_enhanced_email(html_report):
            print("\n🎉 Report with Grafana dashboard sent successfully!")
        else:
            print("\n❌ Failed to send report")

    except Exception as e:
        print(f"\n💥 Error generating report: {str(e)}")
        raise


if __name__ == "__main__":
    main()