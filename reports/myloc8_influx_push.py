import pymysql
from influxdb import InfluxDBClient
from datetime import datetime

# === MySQL Configuration ===
MYSQL_CONFIG = {
    "host": "loc8-aurora-instance-1.crqgu222g0za.eu-central-1.rds.amazonaws.com",
    "user": "trackimo",
    "password": "Daniel81395",
    "database": "trackimo",
    "cursorclass": pymysql.cursors.DictCursor
}

# === InfluxDB Configuration ===
INFLUX_CONFIG = {
    "host": "***********",
    "port": 8086,
    "username": "superadmin",
    "password": "dizzyC@nary96",
    "database": "watchinu_production"
}

# === SQL Queries ===
COMMUNICATING_DEVICES_QUERY = """
SELECT COUNT(DISTINCT d.id) AS communicating_count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
JOIN devices_last_location dll ON d.id = dll.device_id
WHERE dll.updated > NOW() - INTERVAL 30 DAY
  AND ds.fw_version LIKE 'W217_BB12_E_M%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

FIRMWARE_COUNT_QUERY = """
SELECT COUNT(d.id) AS count FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON dad.device_id = d.id
WHERE ds.fw_version LIKE '%W217_BB12_E_M_V12_release%' 
  AND dad.btFWVersion LIKE '%CAT1_BT_V006%';
"""

FIRMWARE_COMMUNICATING_QUERY = """
SELECT COUNT(DISTINCT d.id) AS count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON dad.device_id = d.id
JOIN devices_last_location dll ON dll.device_id = d.id
WHERE dll.updated > NOW() - INTERVAL 30 DAY
  AND ds.fw_version LIKE '%W217_BB12_E_M_V12_release%'
  AND dad.btFWVersion LIKE '%CAT1_BT_V006%';
"""

# === Helper Functions ===
def fetch_single_value(query, key):
    connection = pymysql.connect(**MYSQL_CONFIG)
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            return cursor.fetchone()[key]
    finally:
        connection.close()

def push_to_influx(firmware_count, communicating_count, firmware_comm_count):
    client = InfluxDBClient(**INFLUX_CONFIG)
    current_time = datetime.utcnow().isoformat()

    percentage = round((firmware_comm_count / communicating_count) * 100, 2) if communicating_count else 0.0

    json_point = {
        "measurement": "myloc8_firmware_count",
        "fields": {
            "firmware_count": firmware_count,
            "communicating_count": communicating_count,
            "firmware_communicating_count": firmware_comm_count,
            "percentage": percentage
        },
        "time": current_time
    }

    client.write_points([json_point])
    print(f"Pushed firmware_count = {firmware_count}, communicating = {communicating_count}, "
          f"firmware_communicating = {firmware_comm_count}, percentage = {percentage}% to InfluxDB.")

# === Main Function ===
def main():
    firmware_count = fetch_single_value(FIRMWARE_COUNT_QUERY, 'count')
    communicating_count = fetch_single_value(COMMUNICATING_DEVICES_QUERY, 'communicating_count')
    firmware_comm_count = fetch_single_value(FIRMWARE_COMMUNICATING_QUERY, 'count')

    if firmware_count is not None and communicating_count is not None and firmware_comm_count is not None:
        push_to_influx(firmware_count, communicating_count, firmware_comm_count)
    else:
        print("No data found.")

# === Entry Point ===
if __name__ == "__main__":
    main()
