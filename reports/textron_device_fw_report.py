import pymysql
import requests
import json
from prettytable import PrettyTable
from tabulate import tabulate

# === Email Configuration ===
MANDRILL_API_KEY = '**********************'
SENDER = '<EMAIL>'
RECIPIENTS = ['<EMAIL>']
SUBJECT = 'Textron Devices Firmware Update Report'

# === Database Configuration ===
DB_HOST = 'prod8-aurora.cluster-chxhq6wdghru.us-west-2.rds.amazonaws.com'
DB_USER = 'trackimo'
DB_PASSWORD = 'Daniel81395'
DB_NAME = 'trackimo'

# === Table Formatting Options ===
TABLE_FORMAT = 'grid'  # Options: 'grid', 'fancy_grid', 'simple', 'plain', 'html', 'pipe'


def format_table_with_tabulate(data, headers, title=None):
    """Format table using tabulate library with enhanced styling"""
    table_str = tabulate(data, headers=headers, tablefmt=TABLE_FORMAT, numalign="right", stralign="left")

    if title:
        # Add title with decorative border
        title_border = "=" * max(len(title), len(table_str.split('\n')[0]))
        return f"\n{title_border}\n{title:^{len(title_border)}}\n{title_border}\n\n{table_str}\n"

    return table_str


def format_table_with_prettytable(data, headers, title=None):
    """Format table using PrettyTable with enhanced styling"""
    table = PrettyTable(headers)
    table.align = "l"  # Default left alignment

    # Set specific column alignments
    if "Device Count" in headers or "Count" in headers:
        table.align["Device Count"] = "r"
    if "Device ID" in headers:
        table.align["Device ID"] = "r"

    # Add styling
    table.border = True
    table.header = True
    table.padding_width = 1
    table.junction_char = "+"
    table.horizontal_char = "-"
    table.vertical_char = "|"

    for row in data:
        table.add_row(row)

    if title:
        title_border = "=" * len(str(table).split('\n')[0])
        return f"\n{title_border}\n{title:^{len(title_border)}}\n{title_border}\n\n{table}\n"

    return str(table)


def create_html_table(data, headers, title=None):
    """Create HTML formatted table for email"""
    html = "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; font-family: Arial, sans-serif;'>"

    if title:
        html += f"<caption style='font-size: 16px; font-weight: bold; margin-bottom: 10px;'>{title}</caption>"

    # Headers
    html += "<thead><tr style='background-color: #f2f2f2;'>"
    for header in headers:
        html += f"<th style='text-align: left; padding: 8px;'>{header}</th>"
    html += "</tr></thead>"

    # Data rows
    html += "<tbody>"
    for i, row in enumerate(data):
        bg_color = "#f9f9f9" if i % 2 == 0 else "white"
        html += f"<tr style='background-color: {bg_color};'>"
        for cell in row:
            html += f"<td style='padding: 8px;'>{cell}</td>"
        html += "</tr>"
    html += "</tbody></table>"

    return html


def get_firmware_summary(cursor):
    query = """
    SELECT ds.fw_version, count(ds.fw_version)
    FROM devices d 
    JOIN devices_states ds ON d.id = ds.device_id
    WHERE d.reseller = 282
    GROUP BY ds.fw_version
    ORDER BY count(ds.fw_version) DESC;
    """
    cursor.execute(query)
    results = cursor.fetchall()

    headers = ["Firmware Version", "Device Count"]

    # Return multiple format options
    return {
        'data': results,
        'headers': headers,
        'pretty_table': format_table_with_prettytable(results, headers, "Firmware Summary"),
        'tabulate': format_table_with_tabulate(results, headers, "Firmware Summary"),
        'html': create_html_table(results, headers, "Firmware Summary")
    }


def get_individual_firmware(cursor):
    query = """
    SELECT device_id, fw_version FROM devices_states
    WHERE device_id IN (608053486,608053487,608053488,608053489,608053490,608053491,608053492,608053493,608053494,608053496,608053497,608053498,608053499,608053500,608053501,608053502,608053504,608053505,608053506,608053507,608053508,608053509,608053510,608053511,608053512,608053513,608053514,608053515,608053516,608053517,608053518,608053519,608053520,608053521,608053522,608053523,608053524,608053525,608053526,608053527,608053528,608053529,608053530,608053531,608053532,608053533,608053534,608053535,608053536,608053537,608053538,608053539,608053540,608053541,608053542,608053543,608053544,608053546,608053547,608053548,608053550,608053551,608053552,608053553,608053554,608053555,608053556,608053557,608053558,608053559,608053560,608053561,608053562,608053563,608053564,608053565,608053566,608053567,608053568,608053569,608053570,608053571,608053572,608053573,608053574,608053575,608053576,608053577,608053578,608053579,608053580,608053581,608053582,608053583,608053584,608053585,608053586,608053587,608053588,608053589,608053590,608053591,608053592,608053593,608053594,608053595,608053596,608053597,608053598,608053599,608053600,608053601,608053602,608053603,608053604,608053605,608053606,608053607,608053608,608053609,608053610,608053611,608053612,608053613,608053614,608053615,608053616,608053617,608053618,608053619,608053620,608053621,608053622,608053623,608053624,608053625,608053626,608053627,608053628,608053629,608053630,608053631,608053632,608053634,608053635,608053636,608053637,608053639,608053640,608053641,608053642,608053643,608053644,608053645,608053647,608053649,608053650,608053651,608053652,608053653,608053654,608053656,608053657,608053658,608053659,608053660,608053661,608053662,608053663,608053665,608053666,608053667,608053668,608053669,608053670,608053671,608053672,608053673,608053674,608053675,608053676,608053677,608053678,608053679,608053680,608053681,608053682,608053683,608053684,608053685,608053686,608053687,608053688,608053689,608053691,608053692,608053693,608053694,608053695,608053696,608053697,608053698,608053699,608053700,608053701,608053702,608053703,608053704,608053705,608053706,608053707,608053708,608053709,608053710,608053711,608053712,608053713,608053714,608053715,608053716,608053717,608053718,608053719,608053720,608053721,608053722,608053723,608053724,608053725,608053726,608053727,608053728,608053729,608053730,608053731,608053732,608053733,608053734,608053735,608053736,608053737,608053738,608053739,608053740,608053741,608053742,608053743,608053744,608053745,608053746,608053747,608053748,608053749,608053750,608053751,608053752,608053753,608053754,608053755,608053756,608053757,608053758,608053759,608053760,608053761,608053762,608053763,608053764,608053765,608053766,608053767,608053768,608053769,608053770,608053771,608053772,608053773,608053774,608053775,608053776,608053777,608053778,608053779,608053780,608053781,608053782,608053783,608053784,608053785,608053786,608053787,608053788,608053789,608053790,608053791,608053792,608053793,608053794,608053795,608053796,608053797,608053798,608053799,608053800,608053801,608053802,608053803,608053804,608053805,608053806,608053807,608053808,608053809,608053810,608053811,608053812,608053813,608053814,608053816,608053817,608053818,608053819,608053820,608053821,608053822,608053823,608053824,608053825,608053826,608053827,608053828,608053829,608053830,608053831,608053832,608053833,608053834,608053835,608053836,608053837,608053838,608053839,608053840,608053841,608053842,608053843,608053844,608053845,608053846,608053847,608053848,608053849,608053850,608053851,608053852,608053853,608053854,608053855,608053856,608053857,608053858,608053859,608053860,608053861,608053862,608053863,608053864,608053865,608053866,608053867,608053868,608053869,608053870,608053871,608053872,608053873,608053874,608053875,608053876,608053877,608053878,608053879,608053880,608053881,608053882,608053883,608053884,608053885,608053886,608053887,608053888,608053889,608053890,608053891,608053892,608053893,608053894,608053895,608053896,608053897,608053898,608053899,608053900,608053901,608053902,608053903,608053904,608053905,608053906,608053908,608053909,608053910,608053911,608053912,608053913,608053914,608053915,608053916,608053917,608053918,608053919,608053920,608053921,608053922,608053923,608053924,608053925,608053926,608053927,608053928,608053929,608053930,608053931,608053932,608053933,608053934,608053935,608053936,608053937,608053938,608053939,608053940,608053941,608053942,608053943,608053944,608053945,608053946,608053947,608053948,608053949,608053950,608053951,608053952,608053953,608053954,608053955,608053956,608053957,608053958,608053959,608053960,608053961,608053962,608053963,608053964,608053965,608053966,608053967,608053968,608053969,608053970,608053971,608053972,608053973,608053974,608053975,608053976,608053977,608053978,608053979,608053980,608053981,608053982,608053983,608053984,608053985,608053986,608053987,608053988,608053989,608053990,608053991,608053992,608053993,608053994,608053995,608053996,608053997,608053998,608053999,608054000,608054001,608054002,608054003,608054004,608054005,608054006,608054007,608054008,608054009,608054010,608054011,608054012,608054013,608054014,608054015,608054016,608054017,608054018,608054019,608054020,608054021,608054022,608054023,608054024,608054025,608054026,608054027,608054028,608054029,608054030,608054031,608054032,608054033,608054034,608054035,608054036,608054038,608054039,608054040,608054041,608054042,608054043,608054044,608054045,608054046,608054047,608054048,608054049,608054050,608054051,608054052,608054053,608054054,608054055,608054056,608054057,608054058,608054059,608054060,608054061,608054062,608054063,608054064,608054065,608054066,608054067,608054068,608054069,608054070,608054071,608054072,608054073,608054074,608054075,608054076,608054077,608054078,608054079,608054080,608054081,608054082,608054083,608054084,608054085,608054086,608054087,608054088,608054089,608054090,608054091,608054092,608054093,608054094,608054095,608054096,608054097,608054098,608054099,608054100,608054101,608054102,608054103,608054104,608054105,608054106,608054107,608054108,608054109,608054110,608054111,608054112,608054113,608054114,608054115,608054116,608054117,608054118,608054119,608054120,608054121,608054122,608054123,608054124,608054125,608054126,608054127,608054128,608054129,608054130,608054131,608054132,608054133,608054134,608054136,608054137,608054138,608054139,608054140,608054141,608054142,608054143,608054144,608054145,608054146,608054147,608054148,608054149,608054150,608054151,608054152,608054153,608054154,608054155,608054156,608054157,608054158,608054159,608054160,608054161,608054162,608054163,608054164,608054165,608054166,608054167,608054168,608054169,608054170,608054171,608054172,608054173,608054174,608054175,608054176,608054177,608054178,608054179,608054180,608054181,608054182,608054183,608054184,608054185,608054186,608054187,608054188,608054189,608054190,608054191,608054192,608054193,608054194,608054195,608054196,608054197,608054198,608054199,608054200,608054201,608054202,608054203,608054204,608054205,608054206,608054207,608054208,608054209,608054210,608054211,608054212,608054213,608054214,608054215,608054216,608054217,608054218,608054219,608054220,608054221,608054222,608054223,608054224,608054225,608054226,608054227,608054228,608054229,608054230,608054231,608054232,608054233,608054234,608054235,608054236,608054237,608054238,608054239,608054240,608054241,608054242,608054243,608054244,608054245,608054246,608054247,608054248,608054249,608054250,608054251,608054252,608054253,608054254,608054255,608054256,608054257,608054258,608054259,608054260,608054261,608054262,608054263,608054264,608054265,608054266,608054267,608054268,608054269,608054270,608054271,608054272,608054273,608054274,608054275,608054276,608054277,608054278,608054279,608054280,608054281,608054282,608054283,608054284,608054285,608054286,608054287,608054288,608054289,608054290,608054291,608054292,608054293,608054294,608054295,608054296,608054297,608054298,608054299,608054300,608054302,608054303,608054349,608054350)  -- Replace/add device IDs here
    ORDER BY device_id;
    """
    cursor.execute(query)
    results = cursor.fetchall()

    headers = ["Device ID", "Firmware Version"]

    # Return multiple format options
    return {
        'data': results,
        'headers': headers,
        'pretty_table': format_table_with_prettytable(results, headers, "Individual Device Firmware"),
        'tabulate': format_table_with_tabulate(results, headers, "Individual Device Firmware"),
        'html': create_html_table(results, headers, "Individual Device Firmware")
    }


def send_email(subject, content, html_content=None):
    """Enhanced email function with HTML support"""
    url = 'https://mandrillapp.com/api/1.0/messages/send.json'

    message_data = {
        'from_email': SENDER,
        'to': [{'email': email, 'type': 'to'} for email in RECIPIENTS],
        'subject': subject,
        'text': content
    }

    # Add HTML content if provided
    if html_content:
        message_data['html'] = html_content

    message = {
        'key': MANDRILL_API_KEY,
        'message': message_data
    }

    response = requests.post(url, data=json.dumps(message))
    print("Email sent:", response.status_code, response.text)


def create_summary_stats(summary_data):
    """Create additional summary statistics"""
    total_devices = sum(row[1] for row in summary_data['data'])
    firmware_versions = len(summary_data['data'])

    stats = f"""
    === Summary Statistics ===
    Total Devices: {total_devices}
    Firmware Versions: {firmware_versions}
    Most Common Version: {summary_data['data'][0][0] if summary_data['data'] else 'N/A'}
    """
    return stats


def main():
    try:
        conn = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            cursorclass=pymysql.cursors.Cursor
        )

        with conn.cursor() as cursor:
            summary_data = get_firmware_summary(cursor)
            details_data = get_individual_firmware(cursor)

        # Create summary statistics
        stats = create_summary_stats(summary_data)

        # Compose text report (for console and plain text email)
        text_report = f"""
Textron Devices Firmware Update Report
{'=' * 45}

{stats}

{summary_data['tabulate']}

{details_data['tabulate']}

Report generated automatically.
        """

        # Compose HTML report (for rich email)
        html_report = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <h1 style="color: #333;">Textron Devices Firmware Update Report</h1>

            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3>Summary Statistics</h3>
                <p><strong>Total Devices:</strong> {sum(row[1] for row in summary_data['data'])}</p>
                <p><strong>Firmware Versions:</strong> {len(summary_data['data'])}</p>
                <p><strong>Most Common Version:</strong> {summary_data['data'][0][0] if summary_data['data'] else 'N/A'}</p>
            </div>

            <div style="margin: 20px 0;">
                {summary_data['html']}
            </div>

            <div style="margin: 20px 0;">
                {details_data['html']}
            </div>

            <p style="font-size: 12px; color: #666; margin-top: 30px;">
                Report generated automatically on {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </p>
        </body>
        </html>
        """
        send_email(SUBJECT, text_report.strip(), html_report)

    except Exception as e:
        error_msg = f"Error generating firmware report: {str(e)}"
        print(error_msg)
        send_email(f"ERROR: {SUBJECT}", error_msg)

    finally:
        if 'conn' in locals():
            conn.close()


if __name__ == '__main__':
    main()