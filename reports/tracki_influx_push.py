import pymysql
from influxdb import InfluxDBClient
from datetime import datetime

# === MySQL Configuration ===
MYSQL_CONFIG = {
    "host": "prod8-aurora.cluster-chxhq6wdghru.us-west-2.rds.amazonaws.com",
    "user": "trackimo",
    "password": "Daniel81395",
    "database": "trackimo",
    "cursorclass": pymysql.cursors.DictCursor
}

# === InfluxDB Configuration ===
INFLUX_CONFIG = {
    "host": "**************",
    "port": 8086,
    "username": "superadmin",
    "password": "dizzyC@nary96",
    "database": "trackimo-production"
}

# === Queries ===
FIRMWARE_COUNT_QUERY = """
SELECT COUNT(d.id) AS count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
WHERE ds.fw_version LIKE '%20250509_1042%' 
  AND d.status = true
  AND dad.btFWVersion LIKE 'CAT1_BT_V006_20250421%';
"""

TOTAL_ACTIVE_QUERY = """
SELECT COUNT(*) AS total_active
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
WHERE d.status = true
  AND d.account_id > 0
  AND d.user_id > 0
  AND ds.fw_version LIKE 'W217_BB12_E_T%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

COMMUNICATING_DEVICES_QUERY = """
SELECT COUNT(DISTINCT d.id) AS communicating_count
FROM devices d
JOIN devices_states ds ON d.id = ds.device_id
JOIN device_additional_details dad ON d.id = dad.device_id
JOIN devices_last_location dll ON d.id = dll.device_id
WHERE d.status = true 
  AND d.account_id > 0
  AND d.user_id > 0
  AND dll.updated > NOW() - INTERVAL 30 DAY
  AND ds.fw_version LIKE 'W217_BB12_E_T%'
  AND dad.btFWVersion LIKE 'CAT1_BT%';
"""

# === Generic fetch ===
def fetch_single_value(query, key):
    connection = pymysql.connect(**MYSQL_CONFIG)
    try:
        with connection.cursor() as cursor:
            cursor.execute(query)
            result = cursor.fetchone()
            return result[key]
    finally:
        connection.close()

# === Push to InfluxDB ===
def push_to_influx(firmware_count, total_active, communicating_count):
    client = InfluxDBClient(**INFLUX_CONFIG)
    current_time = datetime.utcnow().isoformat()

    percentage_active = round((firmware_count / total_active) * 100, 2) if total_active else 0.0
    percentage_communicating = round((firmware_count / communicating_count) * 100, 2) if communicating_count else 0.0

    json_point = {
        "measurement": "tracki_universalcat1_firmware_count",
        "fields": {
            "firmware_count": firmware_count,
            "total_active": total_active,
            "communicating_count": communicating_count,
            "percentage_active": percentage_active,
            "percentage_communicating": percentage_communicating
        },
        "time": current_time
    }

    client.write_points([json_point])
    print(f"Pushed firmware={firmware_count}, active={total_active}, communicating={communicating_count}, "
          f"%active={percentage_active}, %communicating={percentage_communicating} to InfluxDB.")

# === Main logic ===
def main():
    firmware_count = fetch_single_value(FIRMWARE_COUNT_QUERY, 'count')
    total_active = fetch_single_value(TOTAL_ACTIVE_QUERY, 'total_active')
    communicating_count = fetch_single_value(COMMUNICATING_DEVICES_QUERY, 'communicating_count')

    if firmware_count is not None:
        push_to_influx(firmware_count, total_active, communicating_count)
    else:
        print("No firmware data found.")

if __name__ == "__main__":
    main()
