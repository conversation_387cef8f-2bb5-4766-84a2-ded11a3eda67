import pymysql
import requests
import json

# === Email Configuration ===
MANDRILL_API_KEY = '**********************'
SENDER = '<EMAIL>'
RECIPIENTS = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>','eitan.lazar<PERSON>@trackimo.com']
SUBJECT = '📋 MyLoc8 Firmware & Bluetooth Breakdown Report'

# === Database Configuration ===
DB_HOST = 'loc8-aurora-instance-1.crqgu222g0za.eu-central-1.rds.amazonaws.com'
DB_USER = 'trackimo'
DB_PASSWORD = 'Daniel81395'
DB_NAME = 'trackimo'

# === Updated Firmware Report Query ===
SQL_FW_REPORT_BRAND_25 = """
SELECT 
    REGEXP_REPLACE(
        SUBSTRING_INDEX(us_fw, '_release_', -1), 
        '^([0-9]{8}_[0-9]{4}).*$', 
        '$1'
    ) AS us_fw_timestamp,
    bt_fw, 
    COUNT(*) AS count
FROM (
   SELECT devices.id,
           devices_states.fw_version AS us_fw,
           device_additional_details.btFWVersion AS bt_fw
    FROM devices
    JOIN devices_states ON devices.id = devices_states.device_id
    JOIN device_additional_details ON device_additional_details.device_id = devices.id
    WHERE devices.brand = 25
      AND devices_states.fw_version LIKE '%W217_BB12%'
    GROUP BY devices.id
) AS t2
GROUP BY REGEXP_REPLACE(
    SUBSTRING_INDEX(us_fw, '_release_', -1), 
    '^([0-9]{8}_[0-9]{4}).*$', 
    '$1'
), bt_fw
ORDER BY count DESC;
"""


def fetch_brand_25_data():
    conn = pymysql.connect(
        host=DB_HOST,
        user=DB_USER,
        password=DB_PASSWORD,
        db=DB_NAME,
        cursorclass=pymysql.cursors.DictCursor
    )
    try:
        with conn.cursor() as cursor:
            cursor.execute(SQL_FW_REPORT_BRAND_25)
            return cursor.fetchall()
    finally:
        conn.close()


def format_brand_25_html(data):
    total_combinations = len(data)
    total_devices = sum(row['count'] for row in data)

    html = """
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, sans-serif;
                background: #f4f4f4;
                padding: 20px;
            }}
            .container {{
                max-width: 900px;
                margin: auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            h2 {{
                text-align: center;
                color: #2c3e50;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }}
            th, td {{
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: #3498db;
                color: white;
            }}
            tr:hover {{ background-color: #f1f1f1; }}
            .summary {{
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h2>📋 MyLoc8 Firmware & Bluetooth Breakdown Report</h2>
            <div class="summary">
                <p>This report shows devices with firmware versions like <strong>W217_BB12</strong>, grouped by firmware timestamp and Bluetooth firmware version.</p>
                <p><strong>Total combinations:</strong> {}</p>
                <p><strong>Total devices:</strong> {}</p>
            </div>
            <table>
                <tr>
                    <th>Firmware Timestamp</th>
                    <th>Bluetooth Firmware</th>
                    <th>Device Count</th>
                </tr>
    """.format(total_combinations, total_devices)

    for row in data:
        html += f"""
                <tr>
                    <td>{row['us_fw_timestamp'] or 'N/A'}</td>
                    <td>{row['bt_fw'] or 'N/A'}</td>
                    <td>{row['count']}</td>
                </tr>
        """
    html += """
            </table>
        </div>
    </body>
    </html>
    """
    return html


def send_email(html_body):
    payload = {
        "key": MANDRILL_API_KEY,
        "message": {
            "from_email": SENDER,
            "to": [{"email": email, "type": "to"} for email in RECIPIENTS],
            "subject": SUBJECT,
            "html": html_body
        }
    }
    response = requests.post("https://mandrillapp.com/api/1.0/messages/send.json", data=json.dumps(payload))
    if response.status_code == 200:
        print("✅ Email sent successfully.")
    else:
        print("❌ Failed to send email:", response.text)


if __name__ == "__main__":
    print("📥 Fetching firmware and bluetooth data for Brand 25...")
    report_data = fetch_brand_25_data()

    if report_data:
        print("📤 Sending firmware breakdown report...")
        html_body = format_brand_25_html(report_data)
        send_email(html_body)
    else:
        print("⚠️ No firmware data found.")