# consts
USER_DIR="/home/<USER>"
VENV_DIR="/home/<USER>/venv/bin/activate"
LOGS_DIR="${USER_DIR}/logs"
PREP="source ${USER_DIR}/.bashrc && source ${VENV_DIR}"

# bitbucket update scripts
55 * * * * /bin/bash -l -c "$PREP && /bin/bash update_trackimo_package.sh 2>> ${LOGS_DIR}/update_trackimo_package.log"
55 * * * * /bin/bash -l -c "$PREP && /bin/bash update_trackimo_operational_scripts.sh 2>> ${LOGS_DIR}/update_trackimo_operational_scripts.log"

# general fota script
SCRIPTS_PREP="source ${USER_DIR}/.bashrc && source ${VENV_DIR} && cd ${USER_DIR}/trackimo_operational_scripts"
#0 * * * * /bin/bash -c "$SCRIPTS_PREP && /bin/bash shell_scripts/fota_master_script_for_env.sh env >> ${LOGS_DIR}/fota_shell_script.log 2>&1"
