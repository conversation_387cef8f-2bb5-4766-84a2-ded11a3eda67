# Trackimo Operational Scripts

## Instructions for Setting Up the 'trackimo_operational_scripts' Repository in a linux based server

### 1. Verify User Login
Ensure that you are logged into the target CAP server as the `fota` user.

### 2. Create an SSH Key
1. As the `fota` user, create an SSH key using the email `<EMAIL>`:
   ```bash
   ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
   ```
2. Save the key in the default location or specify a custom name if needed.

### 3. Add SSH Key to Bitbucket
1. Copy the contents of the public key:
   ```bash
   cat ~/.ssh/id_rsa.pub
   ```
2. Log in to the `lab@trackimo` Bitbucket account.
3. Add the public key to the Bitbucket account. If the environment is `petloc8`, name the key `petloc8 - CAP server`.

### 4. Verify bitbucket connection
1. Run the following command to verify the connection:
   ```bash
   ssh -T *****************
   ```
   - The response should be: 
      ```bash
      authenticated via ssh key.

      You can use git to connect to Bitbucket. Shell access is disabled
      ```

### 5. Create a Virtual Environment
1. Create a virtual environment for the `fota` user:
   ```bash
   python3 -m venv /home/<USER>/venv
   ```
2. Activate the virtual environment:
   ```bash
   source /home/<USER>/venv/bin/activate
   ```

### 6. Add Virtual Environment to `.bashrc`
1. Add the activation command to `.bashrc` so that the virtual environment loads automatically when the `fota` user opens a terminal:
   ```bash
   echo "source /home/<USER>/venv/bin/activate" >> ~/.bashrc
   ```

### 7. Install `trackimo_package`
1. Install the `trackimo_package` module:
   ```bash
   pip install git+ssh://*****************/trackimodev/trackimo_package.git
   ```

### 8. Clone the `trackimo_operational_scripts` Repository
1. Clone the repository into the `fota` root folder:
   ```bash
   <NAME_EMAIL>:trackimodev/trackimo_operational_scripts.git
   ```

### 9. Setup update scripts
1. From `trackimo_operational_scripts` copy the `update_trackimo_package.sh` and `update_trackimo_operational_scripts.sh` scripts to the `fota` root folder (`/home/<USER>/`).
   ```bash
   cp trackimo_operational_scripts/update_trackimo_package.sh /home/<USER>/update_trackimo_package.sh
   cp trackimo_operational_scripts/update_trackimo_operational_scripts.sh /home/<USER>/update_trackimo_operational_scripts.sh
   ```

2. Grant permission to the `update_trackimo_package.sh` and `update_trackimo_operational_scripts.sh` scripts:
   ```bash
   chmod +x /home/<USER>/update_trackimo_package.sh
   chmod +x /home/<USER>/update_trackimo_operational_scripts.sh
   ``` 

### 10. Configure Crontab
1. Create a logs folder for the fota user:
   ```bash
   mkdir -p /home/<USER>/logs
   ```
2. Navigate to the trackimo_operational_scripts folder and open the crontab_commands.txt file.
3. Copy the contents of the file.
4. Open the crontab editor:
   ```bash
   crontab -e
   ```
5. Paste the copied commands into the editor.
6. Update the `env` variable in the cron job for the fota script.
7. Save the file.
---

By following these steps, you will successfully set up the `trackimo_operational_scripts` repository on the CAP server.

## Updating FOTA Packages and Database

### Overview of Chip Types
Devices managed by the FOTA service consist of multiple chips, each responsible for specific functionalities. The primary chip, Unisoc, dictates the compatibility and functionality of the sub-firmwares, which include:

- **Unisoc:** The main chip of the device.
- **Bluetooth (BT):** Handles Bluetooth functionality.
- **GPS:** Manages location-based services.
- **WiFi:** Controls wireless connectivity.

Each of these chips has its own dedicated table in the database to manage firmware packages, along with two additional tables that centralize and log FOTA-related data. In a properly structured `fota_service` schema, there should be six tables:

1. `unisoc_fota_packages` - Firmware for the Unisoc chip.
2. `bt_fota_packages` - Firmware for Bluetooth.
3. `gps_fota_packages` - Firmware for GPS.
4. `wifi_fota_packages` - Firmware for WiFi.
5. `fota_table` - Consolidates all FOTA information, where the Unisoc firmware version serves as the identifier.
6. `fota_status` - Logs all FOTA attempts for devices caught in the FOTA query.

### Adding new FOTA Package - Shared Parameters
All FOTA packages, regardless of type, share the following key parameters:

- **`id`:** A unique identifier assigned to each FOTA package upon creation. This is set automatically but can be manually changed.

- **`target_version_name`:** An identifier for the target firmware version. A meaningful substring of the full version name. For example, `CAT1_WIFI_V005_20241111` can be shortened to `CAT1_WIFI_V005`.

- **`is_active`:** Ensures that if a FOTA message is sent to a specific device with a particular Unisoc firmware version, the update will proceed.

- **`is_valid_for_script`:** Ensures that if a FOTA message is sent to ANY device with a particular Unisoc firmware version, the update will proceed.

### Adding new FOTA Package - Unique Parameters

- **Unisoc:**
  - **`recommended_fota_type`:** Specifies the preferred connection type for the FOTA (either WiFi or Cellular). By default, this is set to WiFi. If a package is too large to update over Cellular, it should be explicitly flagged as WiFi.

- **Bluetooth (BT):**
  - **`bt_fota_link`:** A direct link to the firmware file. This is usually left empty for production firmware but must be verified before adding any new firmware.
  - **`docomo_fota_link`:** A special link required for devices using Docomo SIMs. If a device with a Docomo SIM lacks this link, the FOTA update will be canceled for that device.

## FOTA Table Parameters
Once a FOTA package is added, its `id` must be used in the `fota_table`. This table maps the Unisoc firmware to the corresponding sub-firmwares. The table includes the following six parameters:

1. **`unisoc_fw_name`:** The firmware version name of the Unisoc chip.
2. **`device_type_id`:** References the `id` from `trackimo.devices_types` table.
3. **`unisoc_package_id`:** References the `id` of the Unisoc firmware package.
4. **`bt_package_id`:** References the `id` of the Bluetooth firmware package (nullable).
5. **`wifi_package_id`:** References the `id` of the WiFi firmware package (nullable).
6. **`gps_package_id`:** References the `id` of the GPS firmware package (nullable).

All package IDs can be null, but each `unisoc_fw_name` should be assigned its proper sub-firmware IDs to ensure compatibility.

## FOTA Status Parameters
The fota_status table contains seven columns and tracks detailed information about FOTA attempts. Key columns include:

1. **`id`:** A unique identifier for each FOTA attempt.
2. **`device_id`:** References `id` from `trackimo.devices` table.
3. **`updated`:** The timestamp of when the device received the last FOTA message or during which the device had any errors.
4. **`fota_type`:** Identifies the type of FOTA (Unisoc, BT, WiFi, GPS).
5. **`fota_counter`:** The number of FOTA messages sent to the device before a successful update.
6. **`fw_version`:** The full firmware version name for the device's current `fota_type` fw.
7. **`error`:** A string describing any errors encountered during the FOTA process, or NULL if no errors occurred.

At the end of every FOTA script, the script updates the fota_status table with the results it obtained, ensuring that all attempts are logged for future reference and debugging.

## General FOTA Script
The general script operates on each `fota_type`, as defined in the main shell script.
### 1. Script Variables 
   - **env** The environment is set by the cron job in the crontab, passed as an argument to the shell script (e.g., `... fota_master_script_for_env.sh mst`).
   - **device_types** After verifying that all required files exist, the shell script runs `get_device_type_for_env.py` with the env as an argument. This script retrieves all device types listed in the `fota_table` of the respective environment's `fota_service` schema that have associated package IDs.
   - **fota_type** All available `fota_type` values are predefined in the `fota_master_script_for_env.sh` script. Once valid `device_types` are identified for FOTA, the shell script executes `general_fota.py` with the env, each fota_type, and corresponding device_type as arguments.
      - Scripts for different `device_types` within the same `fota_type` run in parallel.
      - Scripts for different `fota_types` do not run concurrently for the same environment (e.g., bt and unisoc FOTA scripts for mst are sequential).
### 2. Package Discovery Mechanism
When `general_fota.py` is invoked with `env`, `fota_type`, and `device_type`, it queries the `fota_table` and its associated package table (e.g., `unisoc_fota_packages`) to find valid packages.
- Validation Process:
   - The `package_id` entries in the `fota_table` must have both `is_active` and `valid_for_script` flags set to `True`.
   - The `unisoc_fw_name` from the `fota_table` is then collected to identify active devices.
### 3. Device Query and Selection
The script flags a device as valid for FOTA based on specific attributes, ensuring only eligible devices are targeted.
#### General Active Attributes
   - **unisoc_fw** : Matches one of the firmware versions found in the identified packages.
   - **target_{fota_type}_fw** : The target firmware must differ from the firmware in the identified packages.
   - **Report Activity** : The device must have sent a message containing a battery percentage >50% within the last 60 minutes.
   - **No BT_MONITOR** :  The BLE mode in `trackimo.unisoc_bluetooth` must not be 0, as this indicates sleep mode, which can disrupt the FOTA process.
   - **NO RF_OFF** : The last power-up reason for the device must not be `RF_OFF`, as this also indicates sleep mode and could interfere with the FOTA process. process
#### Query Types
The script runs two separate queries:
   1. **Known Devices** : Devices already listed in fota_status for the current fota_type. These devices meet the General Active Attributes and additional criteria:
      - **fota_counter** : Must be less than 5, or between 5 and 10 if the last FOTA message was sent over a day ago.
   2. **Unknown Devices** : Devices not listed in fota_status. These only need to satisfy the General Active Attributes.

## Adding FOTA Scripts from List
If a specific firmware needs to be tested for multiple devices, you can create customized copies of the `fota_type_fota_from_list_for_env.py` script.
### 1. **Add the FOTA Package to the Table** 
   - Important Note:
      - Set `is_valid_for_script` to False if the firmware is still under QA. Otherwise, all devices with the given firmware will receive a FOTA message.
      - Risk : If `is_valid_for_script` is mistakenly set to True, devices with the `unisoc_fw` listed in the FOTA package will receive a FOTA message. This could result in unexpected reboots or installations of unreleased firmware, potentially causing severe issues.
### 2. Copy and Edit the Script
You need to ensure two things after copying the script:
   1. **Set Proper Script Name** 
      - Rename the script according to the required format:`{fota_type_fota_from_list_for_{env}X.py`.
      - For example, if creating a script for Unisoc FOTA in the mst environment, name it unisoc_fota_from_list_for_mst.py. You can also use X to differentiate multiple scripts (e.g., unisoc_fota_from_list_for_mst_Slim.py).
      - If only one script is needed for the same env and fota_type, X can be omitted, but it is recommended to always include the device type as X for clarity.
   2. **Edit the Script Consts** 
      - **env** Currrently it's not auto-assigned so youll need to write it also in the code
      - **fota_type** same as env
      - **device_type_id** Ensure this matches the `device_type_id` in `fota_service.fota_table`. Note that devices do not need to be configured as such; filtering is done by Unisoc firmware.
      - **devices** Populate the list with the relevant device IDs. These will be included in the query as `device_id IN (...)`
### 3. **Push the Repo**
   1. Push the repository changes.
   2. Either wait for the auto-update (handled via a cron job running the update shell script) or manually trigger the update script.
Once all steps are completed, the script will run automatically when invoked by the main process.
