## [3.19.114] - 28/01/2025
- **Improved:** readme file

## [3.19.113] - 26/01/2025
- **Updated:** min battery needed for fota to 30% instead of 50% , can be controlled in get_devices,get_general_active_device_condition

## [3.19.112] - 23/01/2025
- **Fixed:** get random known devices query (missing keyword interval)

## [3.19.111] - 22/01/2025
- **Improved:** known devices selection query for random devices and for devices from list
- **Improved:** readme file
- **Added:** exception when no device types are available

## [3.19.109] - 21/01/2025
- **Added:** Support for multiple scripts per FOTA type in the same environment, with script names in the format {fota_type}_fota_from_list_for_{env}X (suggest replacing X with device type).
- **Improved:** log_path in master_shell script will now be set in the lower function 
- **Improved:** seperated unisoc fota script from list for mst by the device types

## [3.18.107] - 20/01/2025
- **Added:** sys.exit events (2 = no available fota packages, 3 = no available devices)
- **Improved:** main shell script flow and output
- **Fixed:** get known devices from list query and proccess_db function call
- **Fixed:** fota_script_for_list function
- **Improved:** readme file , added fota_from_list section

## [3.16.104] - 19/01/2025
- **Fixed:** call of unisoc_fota_script from list

## [3.16.103] - 17/01/2025
- **Added:** queries for finding valid devices to fota from list
- **Updated:** packages query to include debug packages when list is given
- **Added:** fetch section for fota scripts from list (in master shell script )
- **Added:** dummy fota script from list (fota_type_from_list_for_env.py)
- **Added:** unisoc fota script from list for mst (tracki and slim)

## [3.15.100] - 16/01/2025
- **Added:** device type 'Slim' to device_type_id_dict (.../fota_utils/consts.py)

## [3.15.99] - 15/01/2025
- **Added:** explaination for db control to readme

## [3.14.99] - 09/01/2025
- **Updated:** now logs will be named by device types instead of device ids

## [3.14.98] - 09/01/2025
- **Fixed:** Clarified the exception message for unavailable FOTA packages, with guidance on database configuration.

## [3.14.97] - 08/01/2025
- **Added:** update repo shell scripts
- **Updated:** fw's will now be checked from each env instead of all from dev (golan request)
- **Improved:** device id setup flow (setup per each env)
- **Added:** readme file
- **Updated:** reporter env to trackimo_prod

## [3.12.94] - 30/12/2024
- **Fixed:** timestamps for tklog script for mst bugged devices

## [3.12.93] - 29/12/2024
- **Fixed:** tklog script for mst bugged devices , case where no tklog was found

## [3.12.92] - 26/12/2024
- **Improved:** tklog script for mst bugged devices

## [3.12.91] - 24/12/2024
- **Adjusted:** mst bugged device reporter to eitan's request

## [3.12.90] - 23/12/2024
- **Improved:** mst bugged devices fetcher

## [3.12.89] - 22/12/2024
- **Added:** mst bugged devices reporter
- **Updated:** email list for mst bugged devices reporter

## [3.12.87] - 16/12/2024
- **Updated:** message count for mst crash script

## [3.12.86] - 12/12/2024
- **Added:** crontab command for mst reboot scripts
- **Added:** MST powerup tklog obtain and report

## [3.12.84] - 02/12/2024
- **Added:** wifi and gps fota to the script

## [3.12.83] - 01/12/2024
- **Added:** full support for gps and WiFi FOTA
- **Improved:** the way that the script builds queries for finding devices
- **Improved:** the way bt fota messages are being sent
- **Fixed:** formatting bt version issue
 
## [3.10.80] - 01/12/2024
- **Improved:** , now importing docomo_operators instead of declaring them
- **Updated:** email function according to trackimo_package
- **Added:** universal cat1 and mini cat1 as valid devices types for fota

## [3.10.77] - 13/10/2024
- **Fixed:** FW report
- **Updated:** send_fw_report.sh and crontab commands

## [3.10.75] - 29/09/2024
- **Changed:** envs names that are allowed to fota , watchinu_prod -> trackipet

## [3.10.74] - 26/09/2024
- **Fixed:** missing timezone for comparing timestamps

## [3.10.73] - 25/09/2024
- **Added:** warnings to device dict (block current fota process without effecting future scripts)

## [3.9.73] - 25/09/2024 
- **Fixed:** verifying bt fw version after get_devices_script

## [3.9.72] - 24/09/2024
- **Improved:** crontab commands , venv will be dealt directly by crontab
- **Added:** TODO.md 

## [3.8.71] - 23/09/2024
- **Modified:** use of get_last_messages_by_id for new protocol
- **Modified:** get_devices for new watchinu_prod requirements
- **Improved:** requirements.txt for ssh connection

## [3.8.68] - 17/09/2024
- **Fixed:** main script flow
- **Added:** sleep between each db query (fota_utils.db_update)

## [3.8.66] - 16/09/2024
- **Fixed:** validate venv and verify log shell scripts
 
## [3.8.65] - 15/09/2024
- **Modified:** general BT FOTA currently on hold 

## [3.8.64] - 18/08/2024
- **Modified:** all scripts needed to new trackimo_package structure

## [3.7.64] - 09/07/2024
- **Added:** textron setup script

## [3.6.64] - 03/07/2024
- **Added:** JCI setup script

## [3.5.64] - 20/06/2024
- **Improved:** Instead of dictating sim type will now use operator_name (targed 'KDDI' as docomo)

## [3.4.64] - 20/06/2024
- **Improved:** fetch unknown devices with battery save modes
- **Added:** utils for fetch queries 

## [3.4.62] - 20/06/2024
- **Improved:** fetch known / unknown devices sql query 

## [3.4.61] - 20/06/2024
- **Added:** eitan , shahar and kseniya to the bugged devices report email list
- **Changed:** AWSZ table name to Supports AWSZ in the bugged devices report

## [3.4.59] - 02/06/2024
- **Removed:** possible_errors list from fota_utils

## [3.4.58] - 02/06/2024
- **Added:** get_fw_version_by_type to fota_utils, obtain fw from table instead of messages
- **Changed:** get_updated_devices_dict to work with get_fw_version_by_type instead of getting message for each device

## [3.3.57] - 02/06/2024
- **Changed:** in find_bugged_devices AWSZ paramter name to support_AWSZ (which is more accurate)

## [3.3.56] - 02/06/2024
- **Improved:** find_bugged_devices to work for both universal and curve devices in both watchinu_prod and mst
- **Changed:** find_bugged_curve_devices -> find_bugged_devices
- **Changed:** report_bugged_curve_devices -> report_bugged_devices 
- **Changed:** crontab commands accordingly

## [3.2.53] - 02/06/2024
- **Fixed:** validate venv reinstalling packages

## [3.2.52] - 28/05/2024
- **Fixed:** crontab command to make sure the fota scripts won't run at 15:00

## [3.2.51] - 27/05/2024
- **Removed:** customized_scripts/dummy.py

## [3.2.50] - 27/05/2024
- **Added:** find_bugged_curve_devices
- **Added:** report_bugged_curve_devices

## [3.0.50] - 23/05/2024
- **Fixed:** fota_utils.validate_devices_with_battery_save_mode function's signature

## [3.0.49] - 23/05/2024
- **Improved** changelog use method, now have single file for the entire package (previous changes marked in -P (python) and -S (shell))  

## [2.2.17] - 23/05/2024 (-S)
- **Fixed:** stupid crash in fota_reporter , wrong name for function was called

## [2.2.16] - 23/05/2024 (-S)
- **Fixed:** stupid crash in fota_master , {exit 1;} instead of { exit 1;}

## [2.7.32] - 23/05/2024 (-P)
- **Added:** unisoc_fota_for_watchinu_prod_from_list (eitan's request)
- **Implemented:** control_rf_off in fota_utils
- **Implemented:** control_cpu_off in fota_utils (with max attempt as 1)
- **Improved:** in fota report, unisoc fws that have gps patch and fws with nobands will be mentioned as such 
- **Changed:** bt_fota_for_trackimo_prot_from_list , removed updated devices

## [2.2.15] - 23/05/2024 (-S)
- **Improved:** , generelized customized_scripts.sh
- **Fixed:** currently uninstalling then reinstalling inner packages (update don't work for some reason)
- **Improved:** ensure_python_script function flow (use script full command / main folder)
- **Improved:** documantation for validate venv 
- **Improved:** documantation for verify log
- **Implemented:** run_single_python_script_for_shell in send_fota_report

## [2.1.10] - 22/05/2024 (-S)
- **Chenged:** validate_venv first section of the script (validate that no other cron tasks are running)

## [2.6.28] - 21/05/2024 (-P)
- **Changed:** structure for all scripts
- **Removed:** old customized scripts, now it's empty

## [2.1.9] - 21/05/2024 (-S)
- **Added:** added ensure_python_script_running to fota_master_script_for_env
- **Changed:** update_and_activate_venv -> validate_venv, instead of each run, twice a day , requirements.txt will be proccessed automatically

## [2.0.8] - 21/05/2024 (-S)
- **Changed:** structure for all scripts
- **Removed:** old customized scripts, now it's empty