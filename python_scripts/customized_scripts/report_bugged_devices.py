from trackimo_package import send_email_amazon_ses
from trackimo_package._servers.mysql import _send_query_and_get_results

def generate_html_report(device_data):
    html_report = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Device Report</title>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
            }
            th, td {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }
            th {
                background-color: #f2f2f2;
            }
        </style>
    </head>
    <body>
        <h1>Device Report</h1>
        <table>
            <tr>
                <th>Device ID</th>
                <th>Mah Rate</th>
                <th>Tracking Mode (sample_rate,reports_per_sample) (is_sample_in_minute)</th>
                <th>RF OFF</th>
                <th>CPU OFF</th>
                <th>Supports AWSZ) </th>
                <th>Geozone</th>
                <th>WiFi Geozone</th>
                <th>Unisoc FW Version</th>
                <th>BT FW Version</th>
                <th>Battery Sample Duration</th>
                <th>Battery Report Timestamp</th>
                <th>Shutdown Timestamp</th>
            </tr>
    """

    for data in device_data:
        html_report += """
            <tr>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
                <td>{}</td>
            </tr>
        """.format(*data)

    html_report += """
        </table>
    </body>
    </html>
    """

    return html_report
def create_drained_devices_report(env,email_list:list,times_appeared:int=3):
    _get_devices_query='''
        SELECT device_id
        FROM fota_service.fast_battery_drain_samples
        where shutdown_timestamp > date_sub( now(),interval 8 day )
        GROUP BY device_id
        HAVING COUNT(*) >= %d;
    '''
    full_query = _get_devices_query % times_appeared
    results = _send_query_and_get_results(env,full_query)
    device_ids='('
    for device in results:
        device_ids+=f"{device[0]},"
    device_ids=f"{device_ids[:-1]})"
    
    _get_data_query='''
        SELECT *
        FROM fota_service.fast_battery_drain_samples
        where device_id in %s
            and shutdown_timestamp > date_sub( now(),interval 7 day )
    '''
    full_query = _get_data_query % device_ids
    results = _send_query_and_get_results(env,full_query)
    if results == ():
        raise Exception("No bugged devices were found this week")
    
    html = generate_html_report(results)
    subject="Detailed Report on Devices with Rapid Battery Depletion"
    send_email_amazon_ses(email_list,subject,html=html,sender_name="Bugged Devices")

if __name__ == "__main__":
    
    # validate env
    env="watchinu_prod"
    valid_envs=["watchinu_prod","mst"]
    if not env in valid_envs:
        raise ValueError(f"{env} is not valid for this script , please make sure to select one of those : {valid_envs}")

    # send email to this list
    email_list=["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
    create_drained_devices_report(env,email_list)