from collections import OrderedDict
from datetime import datetime
import json

from trackimo_package import send_email_using_amazon_ses
from trackimo_package.servers.mysql import mysql_general_decorator, _send_query_and_get_results


@mysql_general_decorator()
def find_bugged_devices(env, device_type_identifier: str, expected_msg_id: int = 21, consecutive_count: int = 10,
                        last_hours: int = 24):
    _query = '''
    WITH RankedMessages AS (
        SELECT 
            device_id,
            msg_id,
            created,
            ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY created) - 
            ROW_NUMBER() OVER (PARTITION BY device_id, msg_id ORDER BY created) AS grp
        FROM audit_db.gps_raw_log
        WHERE created > DATE_SUB(NOW(), INTERVAL %d HOUR) AND status_code = 1
    ),
    ConsecutiveCounts AS (
        SELECT 
            device_id,
            msg_id, 
            COUNT(*) AS consecutive_count
        FROM RankedMessages
        WHERE msg_id = %d
        GROUP BY device_id, msg_id, grp
        HAVING COUNT(*) >= %d
    )
    SELECT 
        DISTINCT cc.device_id,
        ds.fw_version,
        MAX(cc.consecutive_count) AS max_consecutive_count
    FROM ConsecutiveCounts cc
    INNER JOIN trackimo.devices_states ds ON ds.device_id = cc.device_id
    WHERE ds.fw_version LIKE '%%%s%%'
    GROUP BY cc.device_id, ds.fw_version
    ORDER BY MAX(cc.consecutive_count) DESC;
    '''
    full_query = _query % (last_hours, expected_msg_id, consecutive_count, device_type_identifier)
    res = _send_query_and_get_results(env, full_query, True)
    if res:
        return [{'device_id': device[0], 'fw_version': device[1], 'max_consecutive_count': device[2]} for device in res]
    return []


@mysql_general_decorator()
def get_fw_versions_matching_identifier(env, identifier: str):
    query = '''
    SELECT DISTINCT fw_version
    FROM trackimo.devices_states
    WHERE fw_version LIKE '%%%s%%'
    ''' % identifier
    res = _send_query_and_get_results(env, query, True)
    return [r[0] for r in res] if res else []


def main_flow(env, to_report=True, device_type_identifiers: list = ['_']):
    result_output = []

    for device_type_identifier in device_type_identifiers:
        bugged_devices = find_bugged_devices(env, device_type_identifier)

        bugged_devices_by_fw = {}
        for device in bugged_devices:
            fw_version = device['fw_version']
            if fw_version not in bugged_devices_by_fw:
                bugged_devices_by_fw[fw_version] = []
            bugged_devices_by_fw[fw_version].append(int(device['device_id']))

        # Only include firmware versions that actually have bugged devices
        for fw_version, device_list in bugged_devices_by_fw.items():
            result_output.append({fw_version: device_list})

    json_result = json.dumps(result_output, indent=2)

    if to_report:
        recipients = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    else:
        recipients = ['<EMAIL>']
        print("Sending report to eden only")

    send_email_using_amazon_ses(
        recipients,
        "MST Bugged Devices JSON Report",
        text=json_result,
        sender_name='MST Device Monitoring'
    )

    print(json_result)


if __name__ == "__main__":
    device_identifiers = ['UC01', 'UB11', 'CURVE2', 'UG02', 'BB12']
    main_flow("mst", device_type_identifiers=device_identifiers)
