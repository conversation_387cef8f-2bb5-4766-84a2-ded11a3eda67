from time import sleep
from datetime import datetime
from trackimo_package import update_device_report_db,send_status_request_message,get_unisoc_fw_version
from trackimo_package.simple_tools.verify_activity import scan_new_message
from trackimo_package.simple_tools.fota import send_unisoc_fota

env = "trackimo_prod"
fota_package_base = '20240203_0959'
devices=[603010496,
603001191,
603002185,
603000829,
603010409,
603010432,
603010439,
603010445,
603010449,
603010452,
603010457,
603010467,
603010488,
603010492,
603009482,
603004033,
603004543]

weird_devices = {
    '20230322_1151' : [603003122,603003274,603003043],
    '20230920_1110' : [603010443]
}

valid_devices={}
before_status=datetime.now().astimezone()

try:
    # 1 - set report to mysql , send staus request and wait for response
    for device_id in devices:
        # set report to mysql
        try:
            update_device_report_db(env,device_id,to_mysql=True)
            valid_devices[device_id]={'report_to_mysql':True}
        except:
            print("failed to set db for ",device_id)
            continue
        
        # send status request for successfull devices
        send_status_request_message(env,device_id)
    sleep(5*60)
        
    are_devices_available = False
    # 2 - make sure device is responsive and base fw is ok
    for device_id in valid_devices:
        valid_devices[device_id]['valid_base_fw'] = False
        if scan_new_message(env,device_id,timestamp=before_status):
            current_unisoc_fw = get_unisoc_fw_version(env,device_id)
            if fota_package_base in current_unisoc_fw :
                valid_devices[device_id]['valid_base_fw'] = True
                if not are_devices_available:
                    are_devices_available=True
    if not are_devices_available:
        raise Exception("No responsive devices were found")
    
    # 3 - send unisoc fota for all valid devices and wait 10 min
    for device_id in valid_devices:
        valid_devices[device_id]['fota_sent']=False
        if not valid_devices[device_id]['valid_base_fw']:
            continue
        send_unisoc_fota(env,device_id)
        valid_devices[device_id]['fota_sent']=True
    sleep(10*60)
    
    # 4 - send status request for all valid devices and wait 5 min
    for device_id in valid_devices:
        if not valid_devices[device_id]['fota_sent']:
            continue
        send_status_request_message(env,device_id)
    sleep(5*60)
    
    # 5 - register final fw
    for device_id in valid_devices:
        if not valid_devices[device_id]['fota_sent']:
            continue
        valid_devices[device_id]['final_fw_version'] = get_unisoc_fw_version(env,device_id)
    
    # 6 - print all valid devices     
    for device_id in valid_devices:
        print(device_id,":",valid_devices[device_id])
except:
    raise
finally:
    for device_id in valid_devices:
        if 'report_to_mysql' in valid_devices[device_id] and valid_devices[device_id]['report_to_mysql']:
            update_device_report_db(env,device_id,to_mysql=False)

