from trackimo_package.servers import add_nickwatch_to_account,get_account_info_dict

if __name__ == "__main__":
    
    # validate env
    env="watchinu_prod"
    account_id = 276293
    devices = [**********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 1001019750,
 1001019804,
 1001019851,
 1001019890,
 1001019931,
 1001020046,
 1001020275,
 1001021123,
 1001016686,
 1001017492,
 1001017840,
 1001017925,
 1001018042,
 1001018091,
 1001018126,
 1001018870,
 1001018912,
 1001018935,
 1001018971,
 1001018982,
 1001019039,
 1001019065,
 1001019103,
 1001019167,
 1001019183,
 1001019241,
 1001019262,
 1001019412,
 1001019430,
 1001019508,
 1001019527,
 1001019541,
 1001019622,
 1001019650,
 1001019695,
 1001019761,
 1001019816,
 1001019852,
 1001019888,
 1001019984,
 1001020215,
 1001020353,
 1001020456,
 1001020683,
 1001020759,
 1001021310,
 1001021334,
 1001021387,
 1001016677,
 1001017084,
 1001017204,
 1001017694,
 1001017782,
 1001018001,
 1001018200,
 1001018223,
 1001018228,
 1001018256,
 1001018896,
 1001019075,
 1001019121,
 1001019180,
 1001019223,
 1001019226,
 1001019236,
 1001019263,
 1001019316,
 1001019321,
 1001019343,
 1001019385,
 1001019581,
 1001019606,
 1001019700,
 1001019713,
 1001019763,
 1001020088,
 1001020114,
 1001020279,
 1001020357,
 1001020360,
 1001020362,
 1001020369,
 1001020436,
 1001020618,
 1001020630,
 1001020896,
 1001020986,
 1001021077,
 1001014625,
 1001017350,
 1001017351,
 1001017439,
 1001017852,
 1001017930,
 1001018069,
 1001018071,
 1001018076,
 1001018093,
 1001018118,
 1001018157,
 1001018266,
 1001018342,
 1001018889,
 1001018902,
 1001018951,
 1001019027,
 1001019077,
 1001019078,
 1001019133,
 1001019242,
 1001019284,
 1001019289,
 1001019315,
 1001019333,
 1001019372,
 1001019382,
 1001019395,
 1001019413,
 1001019521,
 1001019806,
 1001019834,
 1001019928,
 1001019982,
 1001020094,
 1001020260,
 1001020354,
 1001020979,
 1001021401,
 1001017153,
 1001017424,
 1001017532,
 1001017940,
 1001018124,
 1001018131,
 1001018310,
 1001019108,
 1001019109,
 1001019159,
 1001019215,
 1001019264,
 1001019299,
 1001019304,
 1001019306,
 1001019318,
 1001019348,
 1001019388,
 1001019477,
 1001019531,
 1001019551,
 1001019559,
 1001019572,
 1001019621,
 1001019781,
 1001019937,
 1001020011,
 1001020058,
 1001020131,
 1001020148,
 1001020188,
 1001020284,
 1001020382,
 1001020437,
 1001020530,
 1001020565,
 1001020678,
 1001020686,
 1001021114,
 1001021188,
 1001014682,
 1001016713,
 1001017392,
 1001018049,
 1001018082,
 1001018115,
 1001018227,
 1001018246,
 1001018879,
 1001018887,
 1001018905,
 1001018979,
 1001019021,
 1001019026,
 1001019086,
 1001019153,
 1001019251,
 1001019293,
 1001019312,
 1001019344,
 1001019392,
 1001019501,
 1001019505,
 1001019583,
 1001019620,
 1001019745,
 1001019819,
 1001019848,
 1001019875,
 1001019884,
 1001020017,
 1001020040,
 1001020318,
 1001020352,
 1001020553,
 1001021001,
 1001021108,
 1001021127,
 1001021194,
 1001021371,
 1001016799,
 1001017063,
 1001017110,
 1001017167,
 1001017210,
 1001017349,
 1001017524,
 1001017781,
 1001018125,
 1001018231,
 1001018367,
 1001018922,
 1001019005,
 1001019186,
 1001019203,
 1001019229,
 1001019268,
 1001019297,
 1001019305,
 1001019339,
 1001019374,
 1001019490,
 1001019659,
 1001019752,
 1001019782,
 1001019799,
 1001019867,
 1001019941,
 1001020127,
 1001020211,
 1001020317,
 1001020367,
 1001020455,
 1001020528,
 1001020623,
 1001020640,
 1001021014,
 1001021157,
 1001021203,
 1001021315,
 1001014653,
 1001016239,
 1001016680,
 1001017498,
 1001017538,
 1001017557,
 1001017567,
 1001017901,
 1001017978,
 1001018036,
 1001018210,
 1001018331,
 1001018815,
 1001018933,
 1001019024,
 1001019025,
 1001019028,
 1001019055,
 1001019058,
 1001019169,
 1001019191,
 1001019342,
 1001019352,
 1001019376,
 1001019384,
 1001019446,
 1001019447,
 1001019511,
 1001019533,
 1001019549,
 1001019616,
 1001019661,
 1001019751,
 1001019983,
 1001020102,
 1001020198,
 1001020274,
 1001020448,
 1001020980,
 1001021373,
 1001014641,
 1001016661,
 1001016856,
 1001017347,
 1001017555,
 1001017574,
 1001017859,
 1001017959,
 1001017982,
 1001018026,
 1001018035,
 1001018211,
 1001018309,
 1001018930,
 1001018988,
 1001019006,
 1001019029,
 1001019042,
 1001019094,
 1001019142,
 1001019148,
 1001019156,
 1001019231,
 1001019245,
 1001019254,
 1001019276,
 1001019298,
 1001019327,
 1001019379,
 1001019410,
 1001019422,
 1001019499,
 1001019509,
 1001019512,
 1001020201,
 1001020227,
 1001021002,
 1001021060,
 1001021301,
 1001021391,
 1001016695,
 1001017111,
 1001017137,
 1001017543,
 1001017697,
 1001017757,
 1001017759,
 1001017794,
 1001017815,
 1001017829,
 1001017904,
 1001017996,
 1001018050,
 1001018078,
 1001018103,
 1001018106,
 1001018253,
 1001018257,
 1001018279,
 1001018345,
 1001018885,
 1001018904,
 1001018991,
 1001019037,
 1001019111,
 1001019440,
 1001019472,
 1001019553,
 1001019626,
 1001019671,
 1001019731,
 1001019789,
 1001020007,
 1001020383,
 1001020490,
 1001020500,
 1001020920,
 1001021100,
 1001021298,
 1001021318,
 1001016648,
 1001017109,
 1001017152,
 1001017188,
 1001017287,
 1001017491,
 1001017568,
 1001017771,
 1001018022,
 1001018087,
 1001018167,
 1001018192,
 1001018311,
 1001018910,
 1001019008,
 1001019190,
 1001019202,
 1001019214,
 1001019248,
 1001019253,
 1001019291,
 1001019303,
 1001019334,
 1001019345,
 1001019350,
 1001019356,
 1001019378,
 1001019383,
 1001019500,
 1001019532,
 1001019565,
 1001019584,
 1001019980,
 1001020361,
 1001020995,
 1001021008,
 1001021023,
 1001021068,
 1001021085,
 1001021101,
 1001017010,
 1001017536,
 1001017580,
 1001017809,
 1001017846,
 1001017903,
 1001017909,
 1001017980,
 1001018041,
 1001018127,
 1001018215,
 1001018373,
 1001018377,
 1001018898,
 1001018954,
 1001019048,
 1001019117,
 1001019205,
 1001019238,
 1001019250,
 1001019265,
 1001019280,
 1001019347,
 1001019354,
 1001019390,
 1001019432,
 1001019646,
 1001019896,
 1001019969,
 1001020069,
 1001020120,
 1001020358,
 1001020498,
 1001020499,
 1001020556,
 1001020942,
 1001021086,
 1001021359,
 1001021372,
 1001021395,
 1001014627,
 1001014733,
 1001016797,
 1001017260,
 1001017265,
 1001017618,
 1001017788,
 1001017806,
 1001017833,
 1001017976,
 1001018029,
 1001018063,
 1001018073,
 1001018083,
 1001018107,
 1001018943,
 1001019007,
 1001019154,
 1001019187,
 1001019257,
 1001019277,
 1001019279,
 1001019353,
 1001019380,
 1001019458,
 1001019497,
 1001019520,
 1001019563,
 1001019577,
 1001019681,
 1001019840,
 1001019869,
 1001020319,
 1001020605,
 1001020785,
 1001020882,
 1001020943,
 1001021122,
 1001021190,
 1001021208,
 1001016966,
 1001017547,
 1001017588,
 1001017784,
 1001017792,
 1001017905,
 1001017916,
 1001017974,
 1001018052,
 1001018072,
 1001018142,
 1001018155,
 1001018221,
 1001018245,
 1001018298,
 1001018349,
 1001018882,
 1001018895,
 1001018899,
 1001019023,
 1001019068,
 1001019096,
 1001019165,
 1001019184,
 1001019189,
 1001019271,
 1001019351,
 1001019363,
 1001019535,
 1001019786,
 1001019805,
 1001019876,
 1001019919,
 1001020277,
 1001020335,
 1001020443,
 1001021102,
 1001021154,
 1001021160,
 1001021316,
 1001016647,
 1001016673,
 1001016718,
 1001017060,
 1001017064,
 1001017091,
 1001017174,
 1001017819,
 1001017937,
 1001017938,
 1001017954,
 1001017975,
 1001018066,
 1001018147,
 1001018203,
 1001018209,
 1001018241,
 1001018416,
 1001018872,
 1001018873,
 1001018880,
 1001018981,
 1001019035,
 1001019135,
 1001019204,
 1001019267,
 1001019285,
 1001019358,
 1001019576,
 1001019775,
 1001019810,
 1001020003,
 1001020026,
 1001020229,
 1001020238,
 1001020285,
 1001020391,
 1001020621,
 1001021303,
 1001021378,
 1001016627,
 1001016679,
 1001016697,
 1001016699,
 1001017273,
 1001017332,
 1001017537,
 1001017542,
 1001017698,
 1001017767,
 1001017807,
 1001017822,
 1001017828,
 1001017894,
 1001017943,
 1001017946,
 1001017964,
 1001017984,
 1001018043,
 1001018092,
 1001018188,
 1001018230,
 1001018324,
 1001018388,
 1001018409,
 1001018884,
 1001018989,
 1001019018,
 1001019134,
 1001019230,
 1001019310,
 1001019381,
 1001019459,
 1001019753,
 1001020345,
 1001020607,
 1001020918,
 1001021057,
 1001021081,
 1001021159,
 1001014630,
 1001016641,
 1001016737,
 1001017136,
 1001017430,
 1001017535,
 1001017550,
 1001017786,
 1001017868,
 1001017877,
 1001017989,
 1001017991,
 1001018004,
 1001018045,
 1001018243,
 1001018247,
 1001018251,
 1001018272,
 1001018308,
 1001018362,
 1001018390,
 1001018886,
 1001018929,
 1001018932,
 1001019038,
 1001019150,
 1001019163,
 1001019185,
 1001019258,
 1001019269,
 1001019294,
 1001019296,
 1001019431,
 1001019889,
 1001020052,
 1001020153,
 1001020898,
 1001021010,
 1001021070,
 1001021304,
 1001014626,
 1001014628,
 1001015174,
 1001015636,
 1001016685,
 1001016981,
 1001016989,
 1001017061,
 1001017116,
 1001017147,
 1001017151,
 1001017276,
 1001017495,
 1001017544,
 1001017817,
 1001017896,
 1001018010,
 1001018097,
 1001018132,
 1001018156,
 1001018158,
 1001018160,
 1001018190,
 1001018262,
 1001018264,
 1001018269,
 1001018282,
 1001018364,
 1001018958,
 1001019031,
 1001019040,
 1001019193,
 1001019232,
 1001019322,
 1001019346,
 1001019756,
 1001019874,
 1001020984,
 1001021131,
 1001021137,
 1001016626,
 1001016628,
 1001016667,
 1001017092,
 1001017105,
 1001017488,
 1001017552,
 1001017821,
 1001017875,
 1001017890,
 1001017953,
 1001017999,
 1001018056,
 1001018077,
 1001018252,
 1001018290,
 1001018303,
 1001018321,
 1001018338,
 1001018368,
 1001019019,
 1001019045,
 1001019141,
 1001019194,
 1001019244,
 1001019361,
 1001019567,
 1001019585,
 1001020101,
 1001020311,
 1001020359,
 1001020982,
 1001021025,
 1001021036,
 1001021058,
 1001021106,
 1001021107,
 1001021118,
 1001021317,
 1001021344,
 1001014670,
 1001016693,
 1001016896,
 1001016897,
 1001017087,
 1001017280,
 1001017285,
 1001017436,
 1001017466,
 1001017783,
 1001017801,
 1001017874,
 1001017948,
 1001017962,
 1001018020,
 1001018195,
 1001018213,
 1001018263,
 1001018317,
 1001018359,
 1001018391,
 1001018395,
 1001018888,
 1001019105,
 1001019192,
 1001019364,
 1001019387,
 1001019423,
 1001019434,
 1001019599,
 1001019814,
 1001019879,
 1001020081,
 1001020087,
 1001020228,
 1001020350,
 1001020626,
 1001021050,
 1001021053,
 1001021158,
 1001014639,
 1001016633,
 1001016682,
 1001016905,
 1001016974,
 1001017172,
 1001017312,
 1001017327,
 1001017500,
 1001017539,
 1001017540,
 1001017575,
 1001017849,
 1001017955,
 1001017985,
 1001018011,
 1001018085,
 1001018113,
 1001018120,
 1001018301,
 1001018871,
 1001018878,
 1001018907,
 1001018923,
 1001019062,
 1001019131,
 1001019151,
 1001019179,
 1001019199,
 1001019362,
 1001019448,
 1001019449,
 1001019457,
 1001019927,
 1001019992,
 1001020024,
 1001020761,
 1001020762,
 1001021056,
 1001021376,
 1001014721,
 1001016630,
 1001016854,
 1001017085,
 1001017089,
 1001017318,
 1001017503,
 1001017528,
 1001017816,
 1001017826,
 1001017842,
 1001017924,
 1001017934,
 1001017958,
 1001018008,
 1001018025,
 1001018030,
 1001018075,
 1001018141,
 1001018275,
 1001018300,
 1001018326,
 1001018341,
 1001018386,
 1001018936,
 1001019032,
 1001019188,
 1001019335,
 1001019396,
 1001019543,
 1001019548,
 1001019568,
 1001019942,
 1001020289,
 1001020554,
 1001020597,
 1001020694,
 1001021302,
 1001021362,
 1001021384,
 1001016684,
 1001016694,
 1001017082,
 1001017108,
 1001017134,
 1001017255,
 1001017289,
 1001017468,
 1001017593,
 1001017823,
 1001017827,
 1001017892,
 1001017917,
 1001017919,
 1001018021,
 1001018099,
 1001018122,
 1001018170,
 1001018365,
 1001018412,
 1001019034,
 1001019050,
 1001019149,
 1001019196,
 1001019213,
 1001019314,
 1001019340,
 1001019365,
 1001019419,
 1001019725,
 1001020432,
 1001020446,
 1001020489,
 1001020547,
 1001020710,
 1001020789,
 1001020857,
 1001020870,
 1001020998,
 1001021396,
 1001016808,
 1001017175,
 1001017765,
 1001017897,
 1001018175,
 1001018201,
 1001018214,
 1001018222,
 1001018268,
 1001018286,
 1001018376,
 1001019016,
 1001019017,
 1001019066,
 1001019177,
 1001019320,
 1001019349,
 1001019355,
 1001019357,
 1001019371,
 1001019393,
 1001019409,
 1001019566,
 1001019850,
 1001019870,
 1001019936,
 1001019978,
 1001019998,
 1001020065,
 1001020115,
 1001020218,
 1001020226,
 1001020392,
 1001020625,
 1001020636,
 1001021084,
 1001021112,
 1001021125,
 1001021164,
 1001021248,
 1001014655,
 1001016925,
 1001017668,
 1001017675,
 1001017678,
 1001017684,
 1001017686,
 1001017689,
 1001017701,
 1001017706,
 1001017708,
 1001017800,
 1001017865,
 1001018224,
 1001018236,
 1001018283,
 1001018339,
 1001018410,
 1001018749,
 1001018750,
 1001018765,
 1001018770,
 1001018802,
 1001018806,
 1001018824,
 1001018856,
 1001018867,
 1001019136,
 1001019239,
 1001019666,
 1001019809,
 1001020365,
 1001020424,
 1001020585,
 1001020919,
 1001020922,     
 1001021009,
 1001021063,
 1001021087,
 1001021347,
 1001014881,
 1001016701,
 1001017313,
 1001017358,
 1001017573,
 1001017601,
 1001017681,
 1001017736,
 1001017749,
 1001017811,
 1001017910,
 1001018058,
 1001018133,
 1001018735,
 1001018757,
 1001018900,
 1001018903,
 1001018949,
 1001019097,
 1001019110,
 1001019246,
 1001019255,
 1001019308,
 1001019360,
 1001019538,
 1001019710,
 1001019828,
 1001019835,
 1001020015,
 1001020066,
 1001020108,
 1001020366,
 1001020438,
 1001020677,
 1001020838,
 1001020961,     
 1001020996,
 1001021124,
 1001021305,
 1001021374,
 1001016711,
 1001017005,
 1001017677,
 1001017965,
 1001018114,
 1001018807,
 1001019147,
 1001019456,
 1001020349,
 1001014421,
 1001014629,
 1001014681,
 1001014720,
 1001016688,
 1001016765,
 1001016916,
 1001017081,
 1001017129,
 1001017142,
 1001017239,
 1001017435,
 1001017451,
 1001017478,
 1001017648,
 1001017649,
 1001017651,
 1001017653,
 1001017656,
 1001017657,
 1001017659,
 1001017660,
 1001017662,
 1001017664,
 1001017667,
 1001017673,
 1001017679,
 1001017680,
 1001017690,
 1001017691,
 1001017696,
 1001017700,
 1001017707,
 1001017709,
 1001017710,
 1001017711,
 1001017712,
 1001017713,
 1001017714,
 1001017719,
 1001017721,
 1001017722,
 1001017723,
 1001017729,
 1001017735,
 1001017738,
 1001017740,
 1001017741,
 1001017745,
 1001017746,
 1001017750,
 1001017751,
 1001017752,
 1001017756,
 1001017762,
 1001017764,
 1001017766,
 1001017777,
 1001017779,
 1001017790,
 1001017793,
 1001017832,
 1001017836,
 1001017839,
 1001017855,
 1001018095,
 1001018111,
 1001018112,
 1001018139,
 1001018163,
 1001018176,
 1001018180,
 1001018205,
 1001018218,
 1001018233,
 1001018278,
 1001018320,
 1001018404,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********,
 **********]
    print(len(devices))
    ind=281
    for device_id in devices:
        if get_account_info_dict(env,device_id) == {}:
            try:
                add_nickwatch_to_account(env,device_id,account_id,f"WalmartDemo {ind}")
            except Exception as e :
                print(e)
                print("************")
        else:
            continue
    ind+=1
    print(f"Added {ind} devices")