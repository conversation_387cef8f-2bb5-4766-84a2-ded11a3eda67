import pytz
from datetime import datetime,timezone
from trackimo_package import send_email_using_amazon_ses, get_tklog_from_device
from trackimo_package.servers.mysql import mysql_general_decorator, _send_query_and_get_results

def convert_utc_to_japan(utc_timestamp):
    if utc_timestamp.tzinfo is None:
        utc_timestamp = utc_timestamp.replace(tzinfo=timezone.utc)

    # Define the Japan timezone
    japan_timezone = pytz.timezone('Asia/Tokyo')

    # Convert to Japan local time
    japan_time = utc_timestamp.astimezone(japan_timezone)

    return japan_time

@mysql_general_decorator()
def find_bugged_devices(env,expected_msg_id:int=21,consecutive_count:int=10,last_hours:int=24):
    _query='''
        WITH RankedMessages AS ( -- rank all repetative messages by msg id (have the same grp)
        SELECT 
            device_id,
            msg_id,
            created,
            ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY created) - 
            ROW_NUMBER() OVER (PARTITION BY device_id, msg_id ORDER BY created) AS grp
        FROM audit_db.gps_raw_log
        WHERE created > DATE_SUB(NOW(), INTERVAL %d HOUR)
    ),
    ConsecutiveCounts AS ( -- find all repetative 21 messages with more then 10 messages sent and flad the last timestamp for the message in each
     SELECT 
           device_id,
           COUNT(*) AS consecutive_count,
           max(created) as last_bugged_message
        FROM RankedMessages
        where msg_id = %d
        GROUP BY device_id, msg_id, grp
        HAVING COUNT(*) >= %d
        order by consecutive_count desc
   ),
   ActiveDevices AS ( -- find active devices (sent location in the last x minutes and have reported battery > 30)
    SELECT 
        cc.device_id,
        dll.updated as last_location
    FROM ConsecutiveCounts cc
    INNER JOIN trackimo.devices_states ds ON ds.device_id = cc.device_id
    inner join trackimo.devices_last_location dll on dll.device_id = cc.device_id
    WHERE ds.fw_version LIKE '%%UC01%%'
    	and dll.updated > date_sub(now(), interval 12 MINUTE)
    	and dll.battery > 30
    GROUP BY cc.device_id, ds.fw_version
    ORDER BY COUNT(cc.consecutive_count) DESC
    ) -- find active devices (sent location in the last 12 min) that their log can be caught (was sent within the last 60 min)
    select ad.device_id,cc.last_bugged_message,ad.last_location
    from ActiveDevices ad 
    inner join ConsecutiveCounts cc on ad.device_id = cc.device_id 
    where  cc.last_bugged_message > DATE_SUB(now(), INTERVAL 60 MINUTE) 
    	and ad.last_location > cc.last_bugged_message
    group by ad.device_id
    order by cc.last_bugged_message desc
    limit 5;
    '''
    full_query = _query % (last_hours,expected_msg_id,consecutive_count)
    res = _send_query_and_get_results(env,full_query)
    if res:
        return [{'device_id':device[0],'from':convert_utc_to_japan(device[1]),'to':convert_utc_to_japan(device[2])} for device in res]
    return []

def main():
    env = "mst"
    devices = find_bugged_devices(env)
    if devices == []:
        return
    
    log_files = []
    device = devices[0]
    try:
        print(f"Searching for logs for {device['device_id']} between {device['from']} and {device['to']}")
        log_file = get_tklog_from_device(env,device['device_id'],"unexpected_crash",device['from'],device['to'])
        log_files.append(log_file)
    except Exception as e:
        print(f"Failed to get tklog for {device['device_id']} :\n{e}\n")
    
    if len(log_files) == 0:
        print("No Files were found")
        return

    email_list = ['<EMAIL>','<EMAIL>','<EMAIL>']
    try:
        send_email_using_amazon_ses(email_list,"got tklog from mst bugged devices",attachments_paths=[log_files],sender_name='MST Device Monitoring')  
    except:
        email_list.remove(mark_email)
        send_email_using_amazon_ses(email_list,f"got tklog from mst bugged devices, failed to load them to email, please check cap server : {log_files}",sender_name='MST Device Monitoring')
    return True

if __name__ == "__main__":
    print("*****************",datetime.now())
    if main():
        print("Bugged Devices were found and reported")
    else:
        print("No Bugged Devices were found")
