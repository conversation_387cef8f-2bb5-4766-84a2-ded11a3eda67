from datetime import datetime,timedelta

from trackimo_package import get_last_power_event,send_general_setup_with_params,is_device_reporting_to_mysql,update_device_report_db
from trackimo_package.servers.mysql import get_last_messages_by_id

def setup_devices_after_powerup(env:str,devices:list,delay_duration_minutes:int):
    for device_id in devices:
        # make sure devices on prod are reporting to mysql
        if env == "trackimo_prod":
            if not is_device_reporting_to_mysql(env,device_id):
                print(f"{device_id} wasn't reporting to mysql, sending general setup to be safe")
                update_device_report_db(env,device_id,to_mysql=True)
                send_general_setup_with_params(env,device_id) 
                continue
                
        # get last powerup message , check if it's within script time range
        last_powerup = get_last_power_event(env,device_id,is_powerup=True)
        if last_powerup is None or last_powerup['timestamp'] < datetime.now().astimezone() - timedelta(minutes=(delay_duration_minutes*2)):
            continue
        
        # get last setup message , if was sent after last powerup , continue
        last_general_setup=get_last_messages_by_id(env,device_id,is_notification=False,msg_id=47)
        if last_general_setup == [] or last_general_setup[0]['timestamp'] > last_powerup['timestamp']:
            continue
        
        send_general_setup_with_params(env,device_id)

if __name__ == "__main__":
    
    print("*****************",datetime.now())
    
    env = "trackimo_prod"
    devices = [602372018,602372019]
    delay_duration_minutes = 5
    
    setup_devices_after_powerup(env,devices,delay_duration_minutes)