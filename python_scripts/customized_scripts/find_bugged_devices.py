from trackimo_package.servers.webapp import get_device_full_webapp_settings,get_all_geozones
from trackimo_package._servers.mysql import _send_query_and_get_results,_send_query_and_commit

devices={}

devices_dict={
    'Curve 4G':{'type_id':16,'battery_size':500},
    'Universal 4G':{'type_id':0,'battery_size':600},
}

def get_bugged_devices(env,device_type_id,battery_mah,minimum_battery_required=85,max_battery_lost=7):
    _query = '''
        WITH recent_shutdowns AS (
            SELECT grl.device_id, MAX(grl.created) AS shutdown_timestamp
            FROM audit_db.gps_raw_log grl
            inner join trackimo.devices d on grl.device_id = d.id
            inner join trackimo.device_type_features dtf on dtf.id = d.type_and_version 
            WHERE grl.created > DATE_SUB(NOW(), INTERVAL 25 HOUR)
            AND grl.msg_id = 24
            AND grl.parsed_message LIKE '%%LOW_BATTERY_SHUTDOWN%%'
            and dtf.type_id = %d
            GROUP BY grl.device_id
            ORDER BY shutdown_timestamp DESC
        ),
        qualified_battery_reports AS (
            SELECT grl.device_id, 
                   MAX(grl.created) AS last_full_battery_timestamp,
                   CASE
                       WHEN grl.msg_id = 1 THEN JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.battery'))
                       WHEN grl.msg_id = 10 THEN JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryPct'))
                       WHEN grl.msg_id = 18 THEN JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryPercentageStatus'))
                       WHEN grl.msg_id = 37 THEN JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryStatus'))
                       WHEN grl.msg_id = 55 THEN JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryStatus'))
                   END AS battery_status
            FROM audit_db.gps_raw_log grl
            JOIN recent_shutdowns rs ON grl.device_id = rs.device_id
            WHERE grl.created > DATE_SUB(NOW(), INTERVAL 49 HOUR)
            AND grl.created < rs.shutdown_timestamp 
            AND grl.msg_id IN (1, 10, 18, 37, 55)
            AND (
              (grl.msg_id = 1 AND JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.battery')) > %d)
              OR
              (grl.msg_id = 10 AND JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryPct')) > %d)
              OR
              (grl.msg_id = 18 AND JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryPercentageStatus')) > %d)
              OR
              (grl.msg_id = 37 AND JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryStatus')) > %d)
              OR
              (grl.msg_id = 55 AND JSON_UNQUOTE(JSON_EXTRACT(grl.parsed_message, '$.batteryStatus')) > %d)
            )
            GROUP BY grl.device_id
            ORDER BY last_full_battery_timestamp DESC
        )
        SELECT rs.device_id, 
                rs.shutdown_timestamp, qbs.last_full_battery_timestamp, 
                SEC_TO_TIME(TIMESTAMPDIFF(SECOND, qbs.last_full_battery_timestamp, rs.shutdown_timestamp)) AS battery_duration,
                (CAST(qbs.battery_status AS UNSIGNED) - 0) / TIMESTAMPDIFF(HOUR, qbs.last_full_battery_timestamp, rs.shutdown_timestamp) AS battery_loss_per_hour
        FROM recent_shutdowns rs
        JOIN qualified_battery_reports qbs ON rs.device_id = qbs.device_id
        inner join trackimo.unisoc_bluetooth ub on rs.device_id = ub.devices_states_device_id 
        HAVING battery_loss_per_hour > %d
        ORDER BY battery_loss_per_hour DESC
    '''

    full_query = _query % (device_type_id,minimum_battery_required,minimum_battery_required,minimum_battery_required,minimum_battery_required,minimum_battery_required,max_battery_lost)
    results = _send_query_and_get_results(env,full_query)
    if results == ():
        raise Exception("Didn't found devices with deplepted battery")
    global devices
    for device in results:
        devices[device[0]]={'shutdown_timestamp':device[1],'battery_report_timestamp':device[2],'battery_sample_duration':device[3],'mah_rate':float(device[4])*(battery_mah/100)}
    return results
def get_full_data_for_devices_from_db(env):
    device_ids='('
    global devices
    for device in devices:
        device_ids+=f"{device},"
    device_ids=f"{device_ids[:-1]})"
    _query='''
    SELECT ds.device_id, ds.fw_version,ub.fw_version,
        CONCAT('(', ds.tracking_sample_time, ', ', ds.tracking_report_time, ') (', CAST(ds.tracking_in_minutes AS UNSIGNED), ')') AS tracking_mode,
	    CASE WHEN ub.ble_mode = 0 THEN 1 ELSE 0 END AS CPU_OFF , 
	    CASE WHEN dtf.fw_ver LIKE '%%wifi%%' THEN 1 ELSE 0 END AS AWSZ
    from trackimo.devices_states ds
    inner join trackimo.devices d on ds.device_id = d.id
    inner join trackimo.unisoc_bluetooth ub on ds.device_id = ub.devices_states_device_id 
    inner join trackimo.device_type_features dtf on dtf.id = d.type_and_version 
    where ds.device_id in %s
    '''
    full_query = _query % device_ids
    results = _send_query_and_get_results(env,full_query)
    if results == () :
        return None
    for device in results:
        devices[device[0]]['unisoc_fw_version']=device[1]
        devices[device[0]]['bt_fw_version']=device[2]
        devices[device[0]]['tracking_mode']=device[3]
        devices[device[0]]['CPU_OFF']=device[4]
        devices[device[0]]['supports_AWSZ']=device[5]
def get_full_data_for_device_from_webapp(env):
    global devices
    for device_id in devices:
        try:
            results=get_device_full_webapp_settings(env,device_id)
            try:
                rf_off = results['settings']['preferences']['gsm_sleep_mode']
            except:
                rf_off = "false"
            devices[device_id]['RF_OFF']=0 if rf_off == "false" else 1
            fences= get_all_geozones(env,device_id)
            geozone=0
            wifi_geozone=0
            for fence in fences:
                if fence['type'] == "rectangle" and ":device_geozone" in fence['id']:
                    geozone=1
                if fence['type'] == "wifi_geozone" :
                    wifi_geozone=1
            devices[device_id]["geozone"]=geozone
            devices[device_id]["wifi_geozone"]=wifi_geozone
        except:
            print("Failed to get data from webapp for ",device_id)
def inesrt_data_to_table(env):
    global devices
    for device in devices:
        if len(devices[device]) != 12:
            continue
        # Convert battery duration to [hh]:mm:ss format
        total_seconds = devices[device]['battery_sample_duration'].total_seconds()
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        formatted_duration = f"{int(hours):02}:{int(minutes):02}:{int(seconds):02}"

        # Update device data with formatted duration
        devices[device]['battery_sample_duration'] = formatted_duration

        # Constructing the column names and values dynamically
        columns = ', '.join(devices[device].keys())+', device_id'
        values = ', '.join([f"'{value}'" for value in devices[device].values()])+f",{device} "

        # Constructing the full SQL INSERT query
        full_query = f"INSERT INTO fota_service.fast_battery_drain_samples ({columns}) VALUES ({values});"

        try:
            _send_query_and_commit(env,full_query)
        except:
            print("Failed to add line for ",device)

if __name__ == "__main__":
    # validate env
    env="watchinu_prod"
    valid_envs=["watchinu_prod","mst"]
    if not env in valid_envs:
        raise ValueError(f"{env} is not valid for this script , please make sure to select one of those : {valid_envs}")
    
    # validate device type
    type_searched = "Curve 4G"
    if not type_searched in devices_dict:
        raise AssertionError("Unknown device type submitted")
    
    get_bugged_devices(env,devices_dict[type_searched]['type_id'],devices_dict[type_searched]['battery_size'])
    get_full_data_for_devices_from_db(env)
    get_full_data_for_device_from_webapp(env)
    inesrt_data_to_table(env)