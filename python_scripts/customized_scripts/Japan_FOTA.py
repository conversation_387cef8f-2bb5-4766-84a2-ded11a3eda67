from trackimo_package import get_unisoc_fw_version,send_unisoc_fota,is_device_reporting_to_mysql,get_updated_status_report,update_device_report_db

def custom_fota_flow(env,devices,fota_dict):
    for device_id in devices:
        try:
            # make sure device reports to mysql
            if env == "trackimo_prod":
                if not is_device_reporting_to_mysql(env,device_id):
                    update_device_report_db(env,device_id,to_mysql=True)
            
            # make sure device is responding to status request
            if not get_updated_status_report(env,device_id,expected_msg_id=26):
                print(f"{device_id} isn't responding to status request")
    
            # get device's updated unisoc fw
            updated_fw = get_unisoc_fw_version(env,device_id)
            
            # sent unisoc fota if fw is in fota dict
            package_found = False
            for fw_date in fota_dict:
                if fw_date in updated_fw:
                    package_found = True
                    continue

            if package_found:
                send_unisoc_fota(env,device_id)
            else:
                print(f"Device fw is not in fota dict : {updated_fw}")
        except Exception as e:
            print(f"Failed to get data for {device_id} :\n{e}")
        finally:
            if env == "trackimo_prod":
                if not is_device_reporting_to_mysql(env,device_id):
                    update_device_report_db(env,device_id,to_mysql=False)

if __name__ == "__main__":
    env = "trackimo_prod"
    fota_dict = {'20230209_1640':'20230605_1500',
                 '20230605_1500':'20240706_1405'}
    devices = [602117627]
    custom_fota_flow(env,devices,fota_dict)