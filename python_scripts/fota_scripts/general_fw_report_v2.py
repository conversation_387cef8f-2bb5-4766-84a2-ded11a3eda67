import re
import warnings
from datetime import datetime
from trackimo_package import send_email_using_amazon_ses
from trackimo_package.servers.mysql import _send_query_and_get_results

# Constants
production_envs = ['trackimo_prod','mst', 'trackipet']
device_names_dict = {
    'Universal4G': ['Universal4G', 'Universal'],
    'Curve4G': ['Curve4G', 'Mini'],
    'TrackiPro4g': ['TrackiPro4g'],
    'Slim' : ['Slim']
}

email_list = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>']


# SQL Query for fetching report data
def fetch_fota_attempts(env, device_name):
    query = f'''
    WITH devices_fws AS (
        SELECT
            dt.name,
            ds.fw_version AS unisoc_fw,
            ub.fw_version AS bt_fw,
            COUNT(*) AS group_count
        FROM trackimo.devices_states ds
        INNER JOIN trackimo.devices d ON ds.device_id = d.id
        INNER JOIN trackimo.unisoc_bluetooth ub ON ds.device_id = ub.devices_states_device_id
        INNER JOIN trackimo.device_type_features dtf ON dtf.id = d.type_and_version
        INNER JOIN trackimo.device_types dt ON dt.id = dtf.type_id
        WHERE dt.name IN ('{device_name}')
        GROUP BY unisoc_fw, bt_fw
    ),
    unisoc_fota_attempts AS (
        SELECT
            dfw.unisoc_fw,
            dfw.bt_fw,
            COUNT(*) AS fota_attempts
        FROM fota_service.fota_status fs
        INNER JOIN trackimo.devices_states ds ON fs.device_id = ds.device_id
        INNER JOIN trackimo.unisoc_bluetooth ub ON fs.device_id = ub.devices_states_device_id
        INNER JOIN devices_fws dfw ON dfw.unisoc_fw = ds.fw_version AND dfw.bt_fw = ub.fw_version
        WHERE fs.fota_type = 'unisoc'
            AND fs.updated >= NOW() - INTERVAL 7 DAY
            AND fs.fota_counter != 0
        GROUP BY dfw.unisoc_fw, dfw.bt_fw
    ),
    bt_fota_attempts AS (
        SELECT
            dfw.unisoc_fw,
            dfw.bt_fw,
            COUNT(*) AS fota_attempts
        FROM fota_service.fota_status fs
        INNER JOIN trackimo.devices_states ds ON fs.device_id = ds.device_id
        INNER JOIN trackimo.unisoc_bluetooth ub ON fs.device_id = ub.devices_states_device_id
        INNER JOIN devices_fws dfw ON dfw.unisoc_fw = ds.fw_version AND dfw.bt_fw = ub.fw_version
        WHERE fs.fota_type = 'bt'
            AND fs.updated >= NOW() - INTERVAL 7 DAY
            AND fs.fota_counter != 0
        GROUP BY dfw.unisoc_fw, dfw.bt_fw
    )
    SELECT
        dfw.name,
        dfw.unisoc_fw,
        dfw.bt_fw,
        dfw.group_count,
        COALESCE(ufa.fota_attempts, 0) AS unisoc_attempts,
        COALESCE(bfa.fota_attempts, 0) AS bt_attempts
    FROM devices_fws dfw
    LEFT JOIN unisoc_fota_attempts ufa ON ufa.unisoc_fw = dfw.unisoc_fw AND ufa.bt_fw = dfw.bt_fw
    LEFT JOIN bt_fota_attempts bfa ON bfa.unisoc_fw = dfw.unisoc_fw AND bfa.bt_fw = dfw.bt_fw
    ORDER BY dfw.group_count DESC;
    '''
    return _send_query_and_get_results(env, query)


# Generate HTML Report
def generate_html_report():
    html = '<html><head><style>table {border-collapse: collapse;} table, th, td {border: 1px solid black; text-align: center;} th, td {padding: 8px;} th {background-color: #f2f2f2;} tr:nth-child(even) {background-color: #f2f2f2;} tr:nth-child(odd) {background-color: #fff;}</style></head><body>'
    html += f'<h4>Hi Everyone, this is the daily FOTA attempt report :</h4><br>'

    for env in production_envs:
        html += f'<h2>{env.upper()}</h2>'

        for device_type, device_names in device_names_dict.items():
            html += f'<h3>{device_type}</h3>'
            html += '<table border="1">'
            html += '<tr><th>Device Type</th><th>Unisoc FW</th><th>BT FW</th><th>Device Count</th><th>Unisoc FOTA Attempts</th><th>BT FOTA Attempts</th></tr>'

            for device_name in device_names:
                results = fetch_fota_attempts(env, device_name)

                for row in results:
                    html += f'<tr><td>{row[0]}</td><td>{row[1]}</td><td>{row[2]}</td><td>{row[3]}</td><td>{row[4]}</td><td>{row[5]}</td></tr>'

            html += '</table><br>'

    html += '<h4>Report includes devices active in Trackimo-prod and Trackipet-prod.</h4>'
    html += '</body></html>'
    return html


# Send Report
def send_fota_report(debug=False):
    try:
        html = generate_html_report()
    except Exception as e:
        raise Exception("Failed to generate report HTML:", e)

    try:
        if debug:
            print(html)
            return
        send_email_using_amazon_ses(email_list, f'FOTA Attempt Report {datetime.now().strftime("%d/%m/%Y")}', html=html,
                                    sender_name="FOTA Reporter")
    except Exception as e:
        raise Exception("Failed to send email:", e)


if __name__ == "__main__":
    send_fota_report()