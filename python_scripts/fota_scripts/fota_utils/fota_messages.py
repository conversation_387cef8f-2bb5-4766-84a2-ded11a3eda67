from time import sleep

from trackimo_package.servers.message_post import send_ota_start_message,send_unisoc_message
from trackimo_package.simple_tools.fota import docomo_operators,send_unisoc_fota,send_bt_fota,send_wifi_fota,send_gps_fota

sleep_min_dict={
        'bt':7,
        'unisoc':15,
        'wifi':7,
        'gps':7,
}

def send_fota_and_update_device(env,fota_type,package,device):
    if fota_type == 'unisoc':
        send_unisoc_fota(env,device['id'])
    elif fota_type == "bt" :
        # set fota link
        if device['operator_name'] in docomo_operators:
            if package['docomo_fota_link'] is None:
                raise Exception("Docomo fota link not available")
            fota_link = package['docomo_fota_link']
        else:
            fota_link = package['fota_link']
        if fota_link is None:
            fota_link = ''

        send_bt_fota(env,device['id'],fota_link)
    elif fota_type == 'wifi':
        send_wifi_fota(env,device['id'])
    elif fota_type == 'gps':
        send_gps_fota(env,device['id'])
    else:
        raise Exception("Unknown fota type provided : " + fota_type)
    device['fota_counter']+=1
    return device

def process_fota_for_all_devices(fota_dict:dict):
    # try to send all devices fota message
    for device in fota_dict['devices']:
        # skip bugged devices
        if 'error' in device or 'warning' in device:
            continue
        
        # flag package status
        was_package_found = False

        for package in fota_dict['packages']:
            if package['target_version_name'] == device['target_version_name']:
                was_package_found = True
                try:  
                    device = send_fota_and_update_device(fota_dict['env'],fota_dict['type'],package,device)
                except Exception as e:
                    device['error'] = f"script failed to send fota message"
                    print("Failed to send fota message : " + str(e))
        
        # report missing package
        if not was_package_found:
            device['error'] = "fota message wasn't sent , couldn't find device's fota package"
    
    # sleep for required time
    sleep(sleep_min_dict[fota_dict['type']]*60)
    return fota_dict

