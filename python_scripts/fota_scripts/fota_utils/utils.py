from time import sleep
from datetime import datetime
import requests
from trackimo_package.consts import envs
from trackimo_package.advanced_tools import get_tklog_from_device
from trackimo_package.servers.amazon_ses import send_email_using_amazon_ses
from trackimo_package.device_features import control_rf_off,control_cpu_off
from trackimo_package.servers.support_portal import update_device_report_db
from trackimo_package.servers.message_post import send_status_request_message,send_get_single_message
from trackimo_package.servers.mysql import get_last_location

max_fota_attempts=5

#       additional steps
def validate_devices_with_battery_save_mode(fota_dict:dict):
    valid_devices={}
    devices_to_test={}
    # catch devices with battery save mode, save the rest in valid devices
    for id in fota_dict['devices']:
        if fota_dict['devices'][id]['enable_rf_off'] or fota_dict['devices'][id]['enable_cpu_off']:
            devices_to_test[id]=fota_dict['devices'][id]
        else:
            valid_devices[id]=fota_dict['devices'][id]
    
    # if none of the devices needs to be changed by rf_off or cpu off, return
    if fota_dict['devices']==valid_devices:
        return fota_dict['devices']
    before_get_single=datetime.now()
    
    # send get_single to all devices that has battery save mode
    for id in devices_to_test:
        send_get_single_message(fota_dict['env'],id)
    sleep(150)
    
    # disable battery save mode then send status message for server to process request
    for id in devices_to_test:
        last_location=get_last_location(fota_dict['env'],id)
        if last_location and not last_location['timestamp'] > before_get_single:
            continue
        try:
            if devices_to_test[id]['enable_rf_off']:
                control_rf_off(fota_dict['env'],id,to_enable=False)

            if devices_to_test[id]['enable_cpu_off']:
                control_cpu_off(fota_dict['env'],id,enable=False,max_attempts=1)
            sleep(5)

            # after successfully canceling sleep mode, send status message and add to valid devices
            send_status_request_message(fota_dict['env'],id)
            valid_devices[id]=devices_to_test[id]
        except Exception as e:
            try:
                if fota_dict['env'] == "trackimo_prod":
                    update_device_report_db(fota_dict['env'],id,to_mysql=False)
                print(f"Failed to cancel Sleep mode for {id} : {e}")
            except:
                print("after failing to cancel sleep mode, script failed to update device report")
        
    fota_dict['devices']=valid_devices
    return fota_dict['devices']
def return_battery_save_mode_for_devices(fota_dict:dict):
    try:
        for id in fota_dict['devices']:
            if fota_dict['devices'][id]['enable_rf_off']:
                control_rf_off(fota_dict['env'],id,to_enable=True)
            if fota_dict['devices'][id]['enable_cpu_off']:
                control_cpu_off(fota_dict['env'],id,enable=True,max_attempts=1)
    except Exception as e:
        print(f"Couldn't return battery save mode for device : {e}")
def get_tklog_from_bugged_devices(fota_dict:dict,before_fota):
    # set consts
    after_fota=datetime.now()
    tklog_reason=f"device failed to fota {max_fota_attempts}+ times"
    got_log=False
    
    # try to get tklog for each bugged device that failed fota again
    for id in fota_dict['devices']:
        if fota_dict['devices'][id]['fota_done']==False:
            try:
                get_tklog_from_device(fota_dict['env'],id,tklog_reason,before_fota,after_fota)
                print(f"in {fota_dict['env']} got log for {id}: {before_fota}-{after_fota}")
                got_log=True
                fota_dict['devices'][id]['error']="tklog was obtained, please look it up"
            except:
                print(f"failed to get tklog for {id}")
    
    # if got log send email notification
    if got_log:
        try:
            send_email_using_amazon_ses(['<EMAIL>'],"got tklog from bugged device!!!","Hey eden, i got tklog from a bugged device, please check cap server folder",sender_name="FOTA master")
        except:
            print("Failed to send email...")

def login_user(env: str):
    base_urls = {
        'dev': 'https://dev.trackimo.com',
        'stage': 'https://stage.trackimo.com',
        'mst': 'https://mst.trackimo.com',
        'trackimo_prod': 'https://plus.trackimo.com',
        'petloc8' : 'https://app.petloc8.com'
    }

    if env not in base_urls:
        raise ValueError(f"Invalid environment: {env}. Choose from {list(base_urls.keys())}")

    base_url = f"{base_urls[env]}/support-api/api/support/v2/user/login"

    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    payload = {
        "username": "<EMAIL>",
        "password": "Test@123",
        "client_id": "943f9b0f-73c8-4435-8801-0260db687f05",
        "client_secret": "********************************",
        "scopes": "locations,notifications,devices,accounts,settings,geozones"
    }

    response = requests.post(base_url, headers=headers, json=payload)

    try:
        response.raise_for_status()
        access_token = response.json().get("access_token")
        return {
            "token": access_token,
            "base_url": base_urls[env]
        }
    except requests.exceptions.HTTPError as err:
        print(f"Login failed: {err}")
        return None
