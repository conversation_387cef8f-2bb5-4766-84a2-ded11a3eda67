import sys
import time
import warnings

import requests
from lxml.html.diff import token

from .consts import fota_types
from trackimo_package.servers.mysql import _send_query_and_get_results,mysql_general_decorator

from .utils import login_user

# consts
max_fota_in_day=5
max_fota_attempts=30

# query utils
def get_unisoc_fw_name_condition(unisoc_fw_names:list):
    condition="("
    for unisoc_fw_name in unisoc_fw_names:
        condition+=f"(ds.fw_version like '%{unisoc_fw_name}%') or "
    return condition[:-4] + ")"

def get_general_active_device_condition(last_active:int,min_battery:int=30):
    return f"((dll.updated >= (NOW() - INTERVAL {last_active} MINUTE) AND dll.battery > {min_battery} AND dll.updated > dls.updated) OR (dlb.updated >= (NOW() - INTERVAL {last_active} MINUTE) AND dlb.battery > {min_battery} AND dlb.updated > dls.updated))"

def get_filters_per_fota_type(fota_type:str,fota_package:dict):
    if fota_type not in fota_types:
        raise Exception(f"Unknown fota type {fota_type}")
    
    additional_inputs = []
    if fota_type=="bt":
        # updated fw 
        additional_inputs.append('ub.fw_version')
        
        # additional table
        additional_inputs.append('--')
        
        # unisoc fw condition
        additional_inputs.append(get_unisoc_fw_name_condition(fota_package['unisoc_fw_name']))
        
        # bt fota specific conditions
        additional_inputs.append(f"and ub.fw_version not like '%%{fota_package['target_version_name']}%%' and ub.fw_version not in ('',' ')") 
    if fota_type == 'wifi':
        # updated fw
        additional_inputs.append('dad.wifiFWVersion')

        # additional table
        additional_inputs.append('inner join trackimo.device_additional_details dad on d.id = dad.device_id')

        # set unisoc fw filters
        additional_inputs.append(get_unisoc_fw_name_condition(fota_package['unisoc_fw_name']))

        # additional fota_filters
        additional_inputs.append(f"and dad.wifiFWVersion != 'in' and dad.wifiFWVersion not like '%%{fota_package['target_version_name']}%%'")
    if fota_type=="unisoc":
        # updated fw 
        additional_inputs.append('ds.fw_version')
        
        # additional table
        additional_inputs.append('--')

        # unisoc fw condition 
        additional_inputs.append(get_unisoc_fw_name_condition(fota_package['unisoc_fw_name']))
        
        # unisoc fota specific conditions
        additional_inputs.append("--")
    if fota_type == 'gps':
        # updated fw
        additional_inputs.append('dad.externalGPSFW')

        # additional table
        additional_inputs.append('inner join trackimo.device_additional_details dad on d.id = dad.device_id')

        # set unisoc fw filters
        additional_inputs.append(get_unisoc_fw_name_condition(fota_package['unisoc_fw_name']))

        # additional fota_filters
        additional_inputs.append(f"and dad.externalGPSFW != 'in' and dad.externalGPSFW not like '%%{fota_package['target_version_name']}%%'")
    
    return additional_inputs

# processing utils
def process_db_output(fota_type,package,res,are_known_devices:bool=False,is_rf_off_enabled:bool=False,is_cpu_off_enabled:bool=False):
    # turn raw results to a list of devices
    devices = []
    for raw_device in res:
        if fota_type=="bt":
            try:
                current_bt_version = int(raw_device[2].split('V')[1].split('_')[0])
                
                # if device has more advanced bt version, skip it
                if current_bt_version > int(package['target_version_name']):
                    continue

            except:
                # if can't extract bt version, skip this device
                warnings.warn(f"{raw_device[0]} has unknown bt version : {raw_device[2]} , skipping it...")
                continue
        
        device = {}
        # extract data from raw device
        device = {'id':raw_device[0],'operator_name':raw_device[1],'current_fw':raw_device[2]}

        # set consts (based on function input)
        consts = {'rf_off_enabled':is_rf_off_enabled,'cpu_off_enabled':is_cpu_off_enabled,'is_known':are_known_devices}
        if are_known_devices:
            consts['fota_counter'] = raw_device[3]
        else:
            consts['fota_counter'] = 0

        # vars , may differ by fota type
        vars = {'target_version_name':package['target_version_name']}
        
        device.update(consts)
        device.update(vars)
        devices.append(device)
    return devices

# queries for random devices
@mysql_general_decorator()
def get_unknown_devices_for_package(env: str, fota_type: str, package: dict, limit, last_active: int = 60,
                                    to_print: bool = False):
    # Define the base query string. The 'limit' clause will be added conditionally.
    # The placeholders (%s, %d) will be filled by the input_list.
    if fota_type == 'bt':
        # If fota_type is 'bt', the query does not include a LIMIT clause.
        _query = '''
        SELECT d.id , np.operator_name, %s
        from trackimo.devices d
        inner join trackimo.devices_states ds on ds.device_id = d.id
        inner join trackimo.network_operators np on np.operator_id = d.operator_id
        inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
        left join trackimo.devices_last_location dll on dll.device_id = d.id
        left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
        left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
        %s
        -- unisoc fw filter
        where %s
        -- additional fota filters
        %s
        -- filter active devices
        and %s
        -- filter unknown devices for this fota type
        and (d.id,'%s') not in (select device_id,fota_type from fota_service.fota_status)
        group by d.id;
        '''
        # Expected number of inputs for the query when no limit is applied.
        # These correspond to the 6 '%s' placeholders in the query.
        inputs_expected = 6
    else:
        # For other fota_types, the query includes a LIMIT clause.
        _query = '''
        SELECT d.id , np.operator_name, %s
        from trackimo.devices d
        inner join trackimo.devices_states ds on ds.device_id = d.id
        inner join trackimo.network_operators np on np.operator_id = d.operator_id
        inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
        left join trackimo.devices_last_location dll on dll.device_id = d.id
        left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
        left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
        %s
        -- unisoc fw filter
        where %s
        -- additional fota filters
        %s
        -- filter active devices
        and %s
        -- filter unknown devices for this fota type
        and (d.id,'%s') not in (select device_id,fota_type from fota_service.fota_status)
        -- limit count
        group by d.id
        limit %d;
        '''
        # Expected number of inputs for the query when limit is applied.
        # These correspond to the 6 '%s' and 1 '%d' placeholders.
        inputs_expected = 7

    input_list = []

    # Create special filter per each fota type.
    # This function is assumed to return a list of 4 strings that will fill the
    # first four '%s' placeholders in the query (select_columns, join_condition,
    # where_condition, fota_filters).
    fota_specific_inputs = get_filters_per_fota_type(fota_type, package)
    input_list.extend(fota_specific_inputs)

    # Set final inputs for query.
    # This function is assumed to return a string for the 'active_device_condition' placeholder.
    input_list.append(get_general_active_device_condition(last_active))
    # Add the fota_type string for the 'not in' clause.
    input_list.append(fota_type)

    # Conditionally add the limit to the input_list if fota_type is not 'bt'.
    # This will fill the '%d' placeholder in the query.
    if fota_type != 'bt':
        input_list.append(limit)

    # Make sure all inputs were submitted for the chosen query structure.
    if len(input_list) != inputs_expected:
        raise ValueError(
            f"Missing input for devices fetch. Expected {inputs_expected} inputs, but got {len(input_list)}.")

    # Format the query string using the collected input_list.
    # tuple(input_list) is used to unpack the list into arguments for string formatting.
    full_query = _query % tuple(input_list)

    # Send the query and get results.
    res = _send_query_and_get_results(env, full_query, to_print)

    # Process the database output.
    return process_db_output(fota_type, package, res)


@mysql_general_decorator()
def get_known_devices_for_package(env: str, fota_type: str, package: dict, limit, last_active: int = 60,
                                  to_print: bool = False):
    # Define the base query string. The 'limit' clause will be added conditionally.
    # The placeholders (%s, %d) will be filled by the input_list.
    if fota_type == 'bt':
        # If fota_type is 'bt', the query does not include a LIMIT clause.
        _query = '''
        SELECT d.id ,np.operator_name, %s , fs.fota_counter
        from trackimo.devices d
        inner join trackimo.devices_states ds on ds.device_id = d.id
        inner join trackimo.network_operators np on np.operator_id = d.operator_id
        inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
        inner join fota_service.fota_status fs on fs.device_id = d.id
        left join trackimo.devices_last_location dll on dll.device_id = d.id
        left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
        left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
        %s
        -- unisoc fw filter
        where %s
        -- additional fota filters
        %s
        -- filter active devices
        and %s
        -- set filters for known devices
        and fs.fota_type = '%s'
        and fs.error is Null
        and (fs.fota_counter < 5 or (fs.fota_counter < 10 and now() > fs.updated + interval 1 day))
        group by d.id;
        '''
        # Expected number of inputs for the query when no limit is applied.
        # These correspond to the 6 '%s' placeholders in the query.
        inputs_expected = 6
    else:
        # For other fota_types, the query includes a LIMIT clause.
        _query = '''
        SELECT d.id ,np.operator_name, %s , fs.fota_counter
        from trackimo.devices d
        inner join trackimo.devices_states ds on ds.device_id = d.id
        inner join trackimo.network_operators np on np.operator_id = d.operator_id
        inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
        inner join fota_service.fota_status fs on fs.device_id = d.id
        left join trackimo.devices_last_location dll on dll.device_id = d.id
        left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
        left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
        %s
        -- unisoc fw filter
        where %s
        -- additional fota filters
        %s
        -- filter active devices
        and %s
        -- set filters for known devices
        and fs.fota_type = '%s'
        and fs.error is Null
        and (fs.fota_counter < 5 or (fs.fota_counter < 10 and now() > fs.updated + interval 1 day))
        group by d.id
        limit %d;
        '''
        # Expected number of inputs for the query when limit is applied.
        # These correspond to the 6 '%s' and 1 '%d' placeholders.
        inputs_expected = 7

    input_list = []

    # Create special filter per each fota type.
    # This function is assumed to return a list of 4 strings that will fill the
    # first four '%s' placeholders in the query (select_columns, join_condition,
    # where_condition, fota_filters).
    fota_specific_inputs = get_filters_per_fota_type(fota_type, package)
    input_list.extend(fota_specific_inputs)

    # set final inputs for query
    input_list.append(get_general_active_device_condition(last_active))
    input_list.append(fota_type)

    # Conditionally add the limit to the input_list if fota_type is not 'bt'.
    # This will fill the '%d' placeholder in the query.
    if fota_type != 'bt':
        input_list.append(limit)

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError("missing input for devices fetch...")

    if fota_type == 'bt':
        full_query = _query % (
            input_list[0], input_list[1], input_list[2],
            input_list[3], input_list[4], input_list[5]
        )
    else:
        full_query = _query % (
            input_list[0], input_list[1], input_list[2],
            input_list[3], input_list[4], input_list[5],
            input_list[6]
        )
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,are_known_devices=True)

# queries for given devices
@mysql_general_decorator()
def get_unknown_devices_from_list_for_package(env:str,fota_type:str,package:dict,devices:list,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id , np.operator_name, %s
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id = d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
    left join trackimo.devices_last_location dll on dll.device_id = d.id 
    left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
    left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- filter given devices 
    and d.id in (%s)
    and (d.id,'%s') not in (select device_id,fota_type from fota_service.fota_status)
    -- limit count
    group by d.id
    limit %d;
    '''

    inputs_expected=8
    input_list=[]

    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    # activity filter
    input_list.append(get_general_active_device_condition(last_active))
    
    # set expected device ids
    input_list.append(",".join(map(str, devices)))

    # set consts
    input_list.append(fota_type)
    input_list.append(len(devices))

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError("missing input for devices fetch...")
    
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6],input_list[7])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res)

@mysql_general_decorator()
def get_known_devices_from_list_for_package(env:str,fota_type:str,package:dict,devices:list,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id , np.operator_name, %s , fs.fota_counter
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id = d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id = d.id
    inner join fota_service.fota_status fs on fs.device_id = d.id
    left join trackimo.devices_last_location dll on dll.device_id = d.id 
    left join trackimo.devices_last_battery dlb on dlb.device_id = d.id
    left join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id=d.id
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- filter given devices 
    and d.id in (%s)
    and fs.fota_type = '%s'
    and fs.error is Null 
    and (fs.fota_counter < 10)
    -- limit count
    group by d.id
    limit %d;
    '''

    inputs_expected=8
    input_list=[]

    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    input_list.append
    # activity filter
    input_list.append(get_general_active_device_condition(last_active))
    
    # set expected device ids
    input_list.append(",".join(map(str, devices)))

    # set consts
    input_list.append(fota_type)
    input_list.append(len(devices))

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError("missing input for devices fetch...")
    
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6],input_list[7])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,are_known_devices=True)

# main function
def get_devices_for_fota(fota_dict,last_active=60,to_print:bool=False):
    obtain_devices_dict = {
        'unknown' : get_unknown_devices_for_package,
        'known' : get_known_devices_for_package,
        #'known_with_rf_off' : get_known_devices_with_rf_off_for_package,
        #'unknown_with_rf_off' : get_unknown_devices_with_rf_off_for_package,
        #'known_with_cpu_off' :get_known_devices_with_cpu_off_for_package,
        #'unknown_with_cpu_off' :get_unknown_devices_with_cpu_off_for_package,
    }

    devices = []

    db_env = fota_dict['env']
    if fota_dict['env'] == "watchinu_prod":
        db_env += "_trackimo"

    for obtain_devices_method in obtain_devices_dict:
        for package in fota_dict['packages']:
            current_limit = fota_dict['limit'] - len(devices)
            if current_limit == 0 :
                return devices
            print(f"searching for {fota_dict['limit'] - len(devices)} devices flagged as {obtain_devices_method} for {fota_dict['type']} fota package : {package['unisoc_fw_name']} -> {package['target_version_name']}")
            func_used = obtain_devices_dict[obtain_devices_method]
            new_devices = func_used(db_env,fota_dict['type'],package,current_limit,last_active,to_print)
            devices.extend(new_devices)
    if len(devices) == 0 :
        print(f"Script wasn't able to find any devices for {fota_dict['type']} fota in {fota_dict['env']}")
        sys.exit(3) # in main shell script , exit 3 == no avaialble devices
    print(f"found {len(devices)} devices for {fota_dict['type']} fota in {fota_dict['env']}")
    return devices

import requests

def send_gps_location_message(device_id, env):
    auth = login_user(env)

    if not auth:
        print(f"Authentication failed for environment: {env}")
        return

    token = auth["token"]
    base_url = auth["base_url"]
    url = f"{base_url}/support-api/api/backoffice/v1/tools/sendGetSingleGPSLocationMessage"

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    data = {
        'device_id': device_id
    }
    try:
        response = requests.post(url, headers=headers, json=data)
    except requests.exceptions.RequestException as e:
        print(f"❗ Error sending GPS location message for device {device_id}: {e}")


def get_devices_from_list_for_fota(fota_dict,devices, env, last_active=60,to_print:bool=False):
    for device in devices:
        send_gps_location_message(device, env)
    print("waiting for 2 min for make sure that for all the device's last location would be updated")
    time.sleep(120)
    print("starting to fetch the devices from database")
    obtain_devices_dict = {
        'unknown' : get_unknown_devices_from_list_for_package,
        'known' : get_known_devices_from_list_for_package,
    }

    verified_devices = []

    db_env = fota_dict['env']
    if fota_dict['env'] == "watchinu_prod":
        db_env += "_trackimo"

    for obtain_devices_method in obtain_devices_dict:
        for package in fota_dict['packages']:
            current_limit = fota_dict['limit'] - len(verified_devices)
            if current_limit == 0 :
                return verified_devices
            print(f"searching for {fota_dict['limit'] - len(verified_devices)} devices flagged as {obtain_devices_method} for {fota_dict['type']} fota package : {package['unisoc_fw_name']} -> {package['target_version_name']}")
            func_used = obtain_devices_dict[obtain_devices_method]
            new_devices = func_used(db_env,fota_dict['type'],package,devices,current_limit,last_active,to_print)
            verified_devices.extend(new_devices)
    if len(verified_devices) == 0 :
        print(f"Script wasn't able to find any devices for {fota_dict['type']} fota in {fota_dict['env']}")
        sys.exit(3) # in main shell script , exit 3 == no avaialble devices
    print(f"found {len(verified_devices)} devices for {fota_dict['type']} fota in {fota_dict['env']}")
    return verified_devices

# unused queries
@mysql_general_decorator()
def get_known_devices_with_rf_off_for_package(env:str,fota_type:str,package:dict,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id ,np.operator_name , %s , fs.fota_counter
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id=d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id=d.id
    inner join fota_service.fota_status fs on fs.device_id=d.id
    left join trackimo.devices_last_location dll on dll.device_id=d.id
    left join trackimo.devices_last_battery dlb on dlb.device_id=d.id
    inner join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id = ds.device_id 
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- make sure bt_monitor is currently off
    and ub.ble_mode = 1
    -- make sure last shutdown was due to RF_OFF
    and dls.shutdown_reason = "RF_OFF"
    -- set filters for known devices 
    and fs.fota_type = '%s'
    and fs.error is Null 
    and (fs.fota_counter < 5 or fs.fota_counter < 10 and fs.updated + Interval 1 day > now())
    group by d.id
    -- limit count
    limit %d;
    '''
    inputs_expected=7
    input_list=[]

    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    # set final inputs for query
    input_list.append(get_general_active_device_condition(last_active))
    input_list.append(fota_type)
    input_list.append(limit)

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError(f"got unexpected number of inputs, expected {inputs_expected} got {len(input_list)}")
    
    # build full query and get results
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,is_rf_off_enabled=True,are_known_devices=True)

@mysql_general_decorator()
def get_unknown_devices_with_rf_off_for_package(env:str,fota_type:str,package:dict,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id ,np.operator_name , %s
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id=d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id=d.id
    left join trackimo.devices_last_location dll on dll.device_id=d.id
    left join trackimo.devices_last_battery dlb on dlb.device_id=d.id
    inner join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id = ds.device_id 
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- make sure bt_monitor is currently off
    and ub.ble_mode = 1
    -- make sure last shutdown was due to RF_OFF
    and dls.shutdown_reason = "RF_OFF"
    -- filter known devices for this fota type
    and (d.id,'%s') not in (select device_id,fota_type from fota_service.fota_status)
    group by d.id
    -- limit count
    limit %d;
    '''
    inputs_expected=7
    input_list=[]

    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    # set final inputs for query
    input_list.append(get_general_active_device_condition(last_active))
    input_list.append(fota_type)
    input_list.append(limit)

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError(f"got unexpected number of inputs, expected {inputs_expected} got {len(input_list)}")
    
    # build full query and get results
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,is_rf_off_enabled=True)

@mysql_general_decorator()
def get_known_devices_with_cpu_off_for_package(env:str,fota_type:str,package:dict,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id ,np.operator_name , %s , fs.fota_counter
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id=d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id=d.id
    inner join fota_service.fota_status fs on fs.device_id=d.id
    left join trackimo.devices_last_location dll on dll.device_id=d.id
    left join trackimo.devices_last_battery dlb on dlb.device_id=d.id
    inner join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id = ds.device_id 
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- make sure bt_monitor is currently on
    and ub.ble_mode = 0
    -- set filters for known devices 
    and fs.fota_type = '%s'
    and fs.error is Null 
    and (fs.fota_counter < 5 or fs.fota_counter < 10 and fs.updated + Interval 1 day > now())
    group by d.id
    -- limit count
    limit %d;
    '''
    inputs_expected=7
    input_list=[]
    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    # set final inputs for query
    input_list.append(get_general_active_device_condition(last_active))
    input_list.append(fota_type)
    input_list.append(limit)

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError(f"got unexpected number of inputs, expected {inputs_expected} got {len(input_list)}")
    
    # build full query and get results
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,is_cpu_off_enabled=True,are_known_devices=True)

@mysql_general_decorator()
def get_unknown_devices_with_cpu_off_for_package(env:str,fota_type:str,package:dict,limit,last_active:int=60,to_print:bool=False):
    _query='''
    SELECT d.id ,np.operator_name , %s
    from trackimo.devices d
    inner join trackimo.devices_states ds on ds.device_id=d.id
    inner join trackimo.network_operators np on np.operator_id = d.operator_id 
    inner join trackimo.device_last_shutdown dls on dls.device_id=d.id
    left join trackimo.devices_last_location dll on dll.device_id=d.id
    left join trackimo.devices_last_battery dlb on dlb.device_id=d.id
    inner join trackimo.unisoc_bluetooth ub on ub.devices_states_device_id = ds.device_id 
    %s
    -- unisoc fw filter 
    where %s
    -- additional fota filters
    %s
    -- filter active devices
    and %s
    -- make sure bt_monitor is currently on
    and ub.ble_mode = 0
    -- filter known devices for this fota type
    and (d.id,'%s') not in (select device_id,fota_type from fota_service.fota_status)
    group by d.id
    -- limit count
    limit %d;
    '''
    inputs_expected=7
    input_list=[]

    # create special filter per each fota type
    fota_specific_inputs = get_filters_per_fota_type(fota_type,package)
    input_list.extend(fota_specific_inputs)

    # set final inputs for query
    input_list.append(get_general_active_device_condition(last_active))
    input_list.append(fota_type)
    input_list.append(limit)

    # make sure all inputs were submitted:
    if len(input_list)!=inputs_expected:
        raise ValueError(f"got unexpected number of inputs, expected {inputs_expected} got {len(input_list)}")
    
    # build full query and get results
    full_query = _query % (input_list[0],input_list[1],input_list[2],input_list[3],input_list[4],input_list[5],input_list[6])
    res=_send_query_and_get_results(env,full_query,to_print)
    return process_db_output(fota_type,package,res,is_cpu_off_enabled=True)
