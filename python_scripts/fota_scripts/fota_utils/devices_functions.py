from time import sleep
from datetime import datetime

from trackimo_package import update_device_report_db,send_status_request_message,get_last_messages_by_id,get_param_from_any_message

def get_db_verified_devices(devices:list,to_mysql:bool):
    """
    Will go through a list of devices and set their reporting db
    to mysql or redshift according to the to_mysql parameter.
    If a device failed to be set , it will be skipped and not added
    to the verified list.

    Args:
        devices (list): a list of devices that should be verified
        to_mysql (bool): a boolean indicating if the devices should report to mysql or redshift

    Returns:
        list: a list of devices that were successfully set to the desired db

    Raises:
        Exception: if none of the devices were successfully set to the desired db
    """
    for device in devices:
        try:
            update_device_report_db("trackimo_prod",device['id'],to_mysql)
        except:
            if to_mysql:
                device['warning'] = "failed to redirect device to mysql"
            else:
                device['error'] = "failed to redirect device to redshift"
    
    return devices

def update_devices_activity_status(env:str,devices:list,is_after_fota_message:bool=False,sleep_min:int=5):
    """
    Send a status request message to all devices in the list and wait for response.
    If a device didn't respond or the last message was older than the last status request message
    it will be marked with an error.

    Args:
        env (str): the environment in which to communicate with the devices
        devices (list): a list of dictionaries with the devices to be updated
        sleep_min (int): the minimum number of minutes to wait after sending the status request. Default is 5.

    Returns:
        list: the same list of devices with an added 'error' key if the device didn't respond or had an old message
    """
    before_status=datetime.now().astimezone()
    was_sent = False
    # send status request to all devices
    for device in devices:
        # skip devices with error
        if 'error' in device or 'warning' in device:
            continue
        send_status_request_message(env,device['id'])
        if not was_sent:
            was_sent=True
    
    # if no message was sent , return devices
    if not was_sent:
        return devices
    
    # if at least one message was sent go to sleep
    sleep(sleep_min*60)

    # try to get timestamp of any message from each device and check if it's communicating
    for device in devices:
        if 'error' in device or 'warning' in device:
            continue
        try:
            last_notification_timestamp=get_last_messages_by_id(env,device['id'])[0]['timestamp']
        except:
            device['error']="Device didn't sent any messages in the last 6 hours"
            continue
        if before_status > last_notification_timestamp:
            if is_after_fota_message:
                device['warning']="Device didn't respond to status request after fota message"
            device['warning']="Device didn't respond to status request"

    return devices

fw_report_dict={
    'unisoc' : {'msg_id':26,'param':'fwVersion'},
    'bt': {'msg_id':62,'param':'fwVersion'},
    'gps':{'msg_id':54,'param':'externalGPSFW'},
    'wifi':{'msg_id':54,'param':'wifiFWVersion'},
}

def update_devices_fw_version(fota_dict:dict,is_after_fota_message:bool=False,sleep_min:int=5):
    
    # set vars
    env = fota_dict['env']
    fota_type=fota_dict['type']
    devices = fota_dict['devices']
    
    before_status=datetime.now().astimezone()
    devices = update_devices_activity_status(env,devices,is_after_fota_message,sleep_min)
    
    # give additional time for devices to report all status request that were sent 
    sleep(2*60)

    try:
        report_message_id = fw_report_dict[fota_type]['msg_id']
        report_param = fw_report_dict[fota_type]['param']
    except:
        raise Exception("Invalid fota type")
    
    # get current fw for each device which isn't bugged
    for device in devices:
        if 'error' in device or 'warning' in device:
            continue
        
        # get last fw report and make sure it's updated
        last_fw_reports = get_last_messages_by_id(env,device['id'],msg_id=report_message_id)
        if last_fw_reports == []:
            device['error'] = f"Device didn't sent any fw reports for {fota_type} in the last 6 hours"
            continue
        elif before_status > last_fw_reports[0]['timestamp']:
            device['warning'] = f"Device didn't respond with fw report for {fota_type}"
            continue

        # set updated fw version
        try:
            device['current_fw'] = get_param_from_any_message(report_param,last_fw_reports[0]['parsed_message'])
        except:
            device['error'] = f"script failed to extract {fota_type} fw version"
            print(f"script failed to extract fw version for {fota_type}:\nparam = {report_param}\nmessage = {last_fw_reports[0]['parsed_message']}" )
            continue
    return devices