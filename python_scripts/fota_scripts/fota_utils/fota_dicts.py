import sys
from .consts import fota_types
from trackimo_package.servers.mysql import _send_query_and_get_results 

def get_fota_packages_information(env,fota_type,device_type_id,is_script_from_list):
    def merge_packages(packages:list):
        """
        This function takes a list of dictionaries, where each dictionary represents a FOTA package.
        The function will merge packages with the same target_version_name into a single package with a list of unisoc_fw_name.
        The function will return a new list of merged packages.
        """
        merged_packages = []
        for package in packages:
            temp_package = package.copy()
            del temp_package['unisoc_fw_name']

            # check if temp_package is already in merged_packages
            was_found = False
            for merged_package in merged_packages:
                if merged_package['target_version_name'] == temp_package['target_version_name']:
                    was_found = True

            # if was found , add unisoc_fw_name to the pacakge list
            if was_found:
                merged_package['unisoc_fw_name'].append(package['unisoc_fw_name'])
            # else add temp_package
            else:
                temp_package['unisoc_fw_name'] = [package['unisoc_fw_name']]
                merged_packages.append(temp_package)
        return merged_packages

    if fota_type not in fota_types:
        raise ValueError(f"fota_type most exist in fota_types,please make sure to use one of the following : {fota_types}")
    
    base_query = '''
        SELECT ft.unisoc_fw_name , fp.target_version_name %s
        from fota_service.fota_table ft 
        inner join fota_service.%s_fota_packages fp on ft.%s_package_id = fp.id 
        where ft.device_type_id = %d
        and fp.is_active 
        %s
    '''
    
    # additional columns
    if fota_type == 'bt': 
        addtional_cols = ',fp.fota_link,fp.docomo_fota_link'
    else:
        addtional_cols = ''
    
    # additional filters
    if not is_script_from_list:
        addtional_filters = 'and fp.is_valid_for_script\n\t'
    else:
        addtional_filters = '--\n\t'

    if fota_type == 'unisoc':
        addtional_filters += 'and recommended_network_type = \'cellular\''

    full_query = base_query % (addtional_cols,fota_type,fota_type,device_type_id,addtional_filters)

    res = _send_query_and_get_results(env,full_query)
    
    if res == ():
        print(f"Couldn't find any {fota_type} for device type id = {device_type_id}")
        sys.exit(2) # in main shell script , exit 2 == no avaialble fota packages
    packages = []

    if fota_type in ['unisoc','wifi','gps']:
        for package in res:
            packages.append({'unisoc_fw_name':package[0],'target_version_name':package[1]})
    elif fota_type == 'bt':
        for package in res:
            packages.append({'unisoc_fw_name':package[0],'target_version_name':package[1],'fota_link':package[2],'docomo_fota_link':package[3]})
    
    return merge_packages(packages)

def build_fota_dict(env,fota_type,device_type_id,device_count:int=30,is_debug_run:bool=False,is_script_from_list:bool=False):
    fota_dict = {}
    fota_dict['env'] = env
    fota_dict['type'] = fota_type
    fota_dict['packages'] = get_fota_packages_information(env,fota_type,device_type_id,is_script_from_list)
    fota_dict['limit'] = device_count
    fota_dict['to_debug'] = is_debug_run
    return fota_dict

