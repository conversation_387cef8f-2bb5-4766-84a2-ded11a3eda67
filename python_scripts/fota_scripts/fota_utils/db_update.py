import warnings
from time import sleep
from trackimo_package.servers.mysql import _send_query_and_commit,mysql_general_decorator
@mysql_general_decorator()
def update_device_in_db(env,fota_type,device_id,counter,fw_version,error,is_known):
    insert_query = '''
    insert into fota_service.fota_status (fota_counter,fw_version,error,device_id,fota_type)
    VALUES (%d,'%s',%s,%d,'%s');    
    ''' 

    update_query = '''
    UPDATE fota_service.fota_status
    SET fota_counter = %d, fw_version = '%s', error = %s
    WHERE device_id = %d and fota_type = '%s'
    '''

    # set query based on device status
    base_query = update_query if is_known else insert_query

    # set error to NULL or to proper error
    error = 'NULL' if error is None else f'"{error}"'
    try:
        full_query = base_query % (counter,fw_version,error,device_id,fota_type)
        _send_query_and_commit(env,full_query)
    except:
        try:
            warnings.warn(f"Could not update db :\n{full_query}")
        except:
            warnings.warn("Failed to create warning message, could not send query to db")

dp_query_refresh_rate = 5   
def update_db(env,fota_type:str,devices:list):
    # make sure to set temp env correctly
    db_env = env
    if env == "watchinu_prod":
        db_env += "_trackimo"
    for device in devices:
        if device['target_version_name'] in device['current_fw']:
            device['fota_counter'] = 0
        if 'error' not in device:
            device['error'] = None
        update_device_in_db(db_env,fota_type,device['id'],device['fota_counter'],device['current_fw'],device['error'],device['is_known'])
        sleep(dp_query_refresh_rate)

