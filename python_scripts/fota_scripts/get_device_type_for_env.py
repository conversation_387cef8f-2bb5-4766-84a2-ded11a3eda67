from fota_utils.consts import fota_envs
from trackimo_package.servers.mysql import _send_query_and_get_results,mysql_general_decorator

@mysql_general_decorator()
def get_fota_active_device_type_names(env):
    _query='''
    select dt.name
    from fota_service.fota_table ft
    inner join trackimo.device_types dt on dt.id = ft.device_type_id
    left join fota_service.unisoc_fota_packages ufp ON ufp.id = ft.unisoc_package_id
    left join fota_service.bt_fota_packages bfp ON bfp.id = ft.bt_package_id
    left join fota_service.wifi_fota_packages wfp ON wfp.id = ft.wifi_package_id
    left join fota_service.gps_fota_packages gfp ON gfp.id = ft.gps_package_id
    WHERE (ft.unisoc_package_id AND ufp.is_valid_for_script)              -- unisoc
       OR (ft.bt_package_id IS NOT NULL AND bfp.is_valid_for_script)      -- bt
       OR (ft.wifi_package_id IS NOT NULL AND wfp.is_valid_for_script)    -- wifi
       OR (ft.gps_package_id IS NOT NULL AND gfp.is_valid_for_script)     -- gps
    group by dt.id
    '''
    res = _send_query_and_get_results(env,_query)
    if res:
        return [x[0] for x in res]
    return []

if __name__ == '__main__':
    import sys
    args_list = sys.argv[1:]
    if len(args_list) == 0:
        raise ValueError("No args provided")
    if args_list[0] not in fota_envs:
        raise ValueError(f"unknown env provided {args_list[0]} , please select one of those : {fota_envs}")
    env = args_list[0]
    try:
        fota_active_type_ids = get_fota_active_device_type_names(env)
    except:
        raise Exception(f"Failed to get fota active device types, make sure all fota tables are available in {env}")
    if len(fota_active_type_ids) == 0:
        raise Exception(f"No fota active device types found in {env}")
    print(" ".join(map(str, fota_active_type_ids)))