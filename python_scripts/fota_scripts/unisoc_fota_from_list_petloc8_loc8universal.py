from datetime import datetime

from fota_utils import *


def handle_unexpected_events(devices):
    errors = {}
    warnings = {}
    for device in devices:
        if 'warning' in device:
            if device['warning'] not in warnings:
                warnings[device['warning']] = 1
            else:
                warnings[device['warning']] += 1
        if 'error' in device and device['error'] is not None:
            if device['error'] not in errors:
                errors[device['error']] = 1
            else:
                errors[device['error']] += 1

    if len(errors) != 0:
        print("ERRORS:")
        for error in errors:
            print(f"{error} : {errors[error]}")

    if len(warnings) != 0:
        print("WARNINGS:")
        for warning in warnings:
            print(f"{warning} : {warnings[warning]}")


def fota_script_for_list(env, fota_type, device_type_id, devices_ids: list):
    fota_dict = build_fota_dict(env, fota_type, device_type_id, device_count=len(devices_ids), is_script_from_list=True)
    fota_dict['devices'] = get_devices_from_list_for_fota(fota_dict, devices_ids, env)

    if fota_dict['env'] == "trackimo_prod":
        fota_dict['devices'] = get_db_verified_devices(fota_dict['devices'], to_mysql=True)
    try:
        # validate activity and set updated fw version
        fota_dict['devices'] = update_devices_fw_version(fota_dict, is_after_fota_message=False)

        # send fota message according to each package used
        print("about to start send ota::")
        fota_dict = process_fota_for_all_devices(fota_dict)
        print("processed ota::")
        # validate activity and set updated fw version after fota
        fota_dict['devices'] = update_devices_fw_version(fota_dict, is_after_fota_message=True)

        # update in db
        update_db(fota_dict['env'], fota_dict['type'], fota_dict['devices'])

    finally:
        if fota_dict['env'] == "trackimo_prod":
            get_db_verified_devices(fota_dict['devices'], to_mysql=False)

        handle_unexpected_events(fota_dict['devices'])


if __name__ == "__main__":
    env = "petloc8"
    fota_type = "unisoc"
    device_type_name = 'Loc8Universal'
    device_type_id = device_type_id_dict[device_type_name]  # 28
    device_ids = [623012541]
    print("*********************************************************************")
    print(datetime.now().strftime("%Y-%m-%d %H:%M"))
    fota_script_for_list(env, fota_type, device_type_id, device_ids)



