from datetime import datetime

from fota_utils import *

min_devices_for_fota = 3
max_devices_for_fota = 50


def handle_unexpected_events(devices):
    errors = {}
    warnings = {}
    for device in devices:
        if 'warning' in device:
            if device['warning'] not in warnings:
                warnings[device['warning']] = 1
            else:
                warnings[device['warning']] += 1
        if 'error' in device and device['error'] is not None:
            if device['error'] not in errors:
                errors[device['error']] = 1
            else:
                errors[device['error']] += 1

    if len(errors) != 0:
        print("ERRORS:")
        for error in errors:
            print(f"{error} : {errors[error]}")

    if len(warnings) != 0:
        print("WARNINGS:")
        for warning in warnings:
            print(f"{warning} : {warnings[warning]}")


def fota_script(fota_dict: dict, devices: list = None):
    # get / validate initial devices 
    if devices is None:
        fota_dict['devices'] = get_devices_for_fota(fota_dict)
    else:
        if len(devices) == 0:
            raise Exception("Empty devices list was provided, thanks champ")
        fota_dict['devices'] = devices

    if fota_dict['env'] == "trackimo_prod":
        fota_dict['devices'] = get_db_verified_devices(fota_dict['devices'], to_mysql=True)
    try:
        # validate activity and set updated fw version
        fota_dict['devices'] = update_devices_fw_version(fota_dict, is_after_fota_message=False)

        ## make an attempt to CANCEL BATTERY SAVE MODE IF ACTIVE

        # send fota message according to each package used
        fota_dict = process_fota_for_all_devices(fota_dict)

        # validate activity and set updated fw version after fota
        fota_dict['devices'] = update_devices_fw_version(fota_dict, is_after_fota_message=True)

        ## RETURN BATTERY MODES TO DEVICES

        # UPDATE RESULTS IN DB
        update_db(fota_dict['env'], fota_dict['type'], fota_dict['devices'])

    finally:
        if fota_dict['env'] == "trackimo_prod":
            get_db_verified_devices(fota_dict['devices'], to_mysql=False)

        handle_unexpected_events(fota_dict['devices'])


if __name__ == "__main__":
    import sys


    def build_fota_dict_from_user_input(string_list: list):

        # every fota format is : <env> <fota_type> <device_type> <device_count*> <to_debug*>
        assert len(string_list) >= 3, "Missing information ..."

        # set and verify <env>
        env = string_list[0]
        assert env in fota_envs, f"fota script are not allowed to run in {env}, please select one of those in {fota_envs}"

        # set and verify <fota_type>
        fota_type = string_list[1]
        assert fota_type in fota_types, f"Unsupported type of fota provided , please select one of those in {fota_types}"

        # set and verify <device_type> (id or name)
        device_type = string_list[2]
        try:
            device_type_id = int(device_type)
            assert device_type_id in device_type_id_dict.values(), f"Unknown device type id provided {device_type_id}, please select one of thsose {device_type_id_dict.values()}"
        except:
            device_type_name = str(device_type)
            assert device_type_name in device_type_id_dict, f"Unknown device type name provided {device_type_name}, please select one of thsose {device_type_id_dict.keys()}"
            device_type_id = device_type_id_dict[device_type_name]

        if len(string_list) == 3:
            return build_fota_dict(env, fota_type, device_type_id)

        # check arg[4] is int (device count)
        is_device_count = True
        try:
            device_count = int(string_list[3])
        except:
            is_device_count = False

        if is_device_count or len(string_list) == 5:
            assert device_count >= min_devices_for_fota, f"Requested to do fota for less then the minimum (Req : {device_count}, Min:{min_devices_for_fota})"
            assert device_count <= max_devices_for_fota, f"Requested to do fota for more then the maximum (Req : {device_count}, Max:{max_devices_for_fota})"

        if is_device_count and len(string_list) == 4:
            return build_fota_dict(env, fota_type, device_type_id_dict[device_type_name], device_count)
        debug_index = 3 if len(string_list) == 4 else 4
        assert string_list[debug_index].upper() in ['TRUE',
                                                    'FALSE'], "Thats not a string representation of a boolean..."
        to_debug = string_list[debug_index].upper() == 'TRUE'
        if len(string_list) == 4:
            return build_fota_dict(env, fota_type, device_type_id_dict[device_type_name], is_debug_run=to_debug)
        return build_fota_dict(env, fota_type, device_type_id_dict[device_type_name], device_count,
                               is_debug_run=to_debug)


    try:
        args_list = sys.argv[1:]
    except:
        raise ValueError("Couldn't find any args for script")
    fota_dict = build_fota_dict_from_user_input(args_list)
    print("*********************************************************************")
    print(datetime.now().strftime("%Y-%m-%d %H:%M"))
    fota_script(fota_dict)
