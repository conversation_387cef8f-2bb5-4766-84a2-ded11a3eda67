import re
import warnings
from datetime import datetime
from trackimo_package import send_email_using_amazon_ses
from trackimo_package.servers.mysql import _send_query_and_get_results

#   const  
production_envs=['trackimo_prod','trackipet']
device_names_dict={
    'Universal4G':['Universal4G','Universal'],
    'Curve4G':['Curve4G','Mini'],
    'TrackiPro4g':['TrackiPro4g'],
}

unisoc_fota_status_dict={}

#    vars
unisoc_min = 100
bt_min = 20
email_list = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>']


#   utils
def _get_fw_name(full_fw,fw_type:str='UNISOC'):
    if fw_type.upper()=='UNISOC':
        # flag no_band versions
        is_no_band_version=False
        band_removed_ind=None
        if "_NO_BAND" in full_fw:
            is_no_band_version=True
            band_match = re.search(r"_NO_BAND(\d+)_", full_fw)
            if band_match:
                band_removed_ind=int(band_match.group(1))
        
        # flag patched versions
        is_patched_version=False
        if "_P_" in full_fw or "_EP_" in full_fw or "20231110_1710" in full_fw:
            is_patched_version=True
        
        # try to set fw_name by using known unisoc formats
        fw_name=None
        
        # use current format
        current_fw_format = r'\b(\d{8}_\d{4})\b'
        match = re.search(current_fw_format, full_fw)
        if match:
            fw_name=match.group(0) 
        
        # if fw name wasn't set, try to filter by older formats
        if fw_name is None:
            # use old format
            old_fw_format1 = r'\b(\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2})\b'
            match = re.search(old_fw_format1, full_fw)
            if match:
                date_obj = datetime.strptime(match.group(0), '%m-%d-%Y %H:%M:%S')
                fw_name=date_obj.strftime('%Y%m%d_%H%M')
        
        # if fw name wasn't found, raise warning
        if fw_name is None:
            warnings.warn(f"Couldn't find fw name (date) for {full_fw},please consider adding another unisoc fw format")
            return

        # if fw has any specific configuration mention it 
        if is_no_band_version:
            addition="_NO_BAND"
            if band_removed_ind is not None:
                addition+=str(band_removed_ind)
            fw_name=addition+"_"+fw_name
        if is_patched_version:
            fw_name="_P_"+fw_name
        
        return fw_name
    elif fw_type.upper()=='BT':
        if not full_fw:
            return "bt fw missing"
        main_pattern = r'BT_V(\d{3})_'
        match = re.search(main_pattern, full_fw)
        if match:
            return str(int(match.group(1)))

def _get_target_unisoc_fw(unisoc_fw):
    _query='''
            SELECT ufp.target_version_name , ufp.recommended_network_type 
            from fota_service.fota_table ft 
            inner join fota_service.unisoc_fota_packages ufp on ufp.id = ft.unisoc_package_id 
            where ft.unisoc_fw_name = '%s'
            and ufp.is_active '''
    full_query = _query % unisoc_fw
    res = _send_query_and_get_results("trackimo_prod",full_query)
    if res:
        return f"-> {res[0][0]} ({res[0][1]})"
    else:
        return 'N\\A'

def get_fw_dict(device_names):
    _query='''
    SELECT unisoc.fw_version as unisoc_fw , bt.fw_version as bt_fw , COUNT(d.id) as device_count
     -- target specific device type
    FROM trackimo.devices d
    INNER JOIN trackimo.device_type_features dtf ON d.type_and_version = dtf.id
    INNER JOIN trackimo.device_types dt ON dt.id = dtf.type_id
    -- monitor activity
    INNER JOIN trackimo.devices_last_location dll ON d.id = dll.device_id
    -- fws
    INNER JOIN trackimo.unisoc_bluetooth bt on d.id = bt.devices_states_device_id
    INNER join trackimo.devices_states unisoc on d.id = unisoc.device_id 
    WHERE dll.updated > DATE_SUB(NOW(), INTERVAL 1 MONTH)
    AND dt.name IN (%s)
    AND unisoc.fw_version LIKE '%%buildtime%%'
    GROUP BY unisoc.fw_version,bt.fw_version
    order by device_count desc;
    '''
    full_query=_query % ', '.join([f'"{item}"' for item in device_names])
    
    results = {}
    results['unisoc']={}
    results['bt']={}
    
    
    # process data in each env and add it to results
    for env in production_envs:
        results['unisoc'][env]={}
        results['bt'][env]={}
        
        temp_results=_send_query_and_get_results(env,full_query)
        
        # row - (unisoc_fw,bt_fw,count)
        for row in temp_results:
            
            # save unisoc fw:  
            unisoc_fw_name=_get_fw_name(row[0],"unisoc")
            
            # if was able to get proper unisoc fw name
            if unisoc_fw_name is not None: 
                if unisoc_fw_name in results['unisoc'][env]:
                    results['unisoc'][env][unisoc_fw_name]+=row[2]
                else:
                    results['unisoc'][env][unisoc_fw_name]=row[2]
            
            # save bt fw:
            bt_fw_name=_get_fw_name(row[1],"bt")
            # if was able to get proper unisoc fw name
            if bt_fw_name is not None: 
                if bt_fw_name in results['bt'][env]:
                    results['bt'][env][bt_fw_name]+=row[2]
                else:
                    results['bt'][env][bt_fw_name]=row[2]
    return results

def get_device_html_section(device_type,data):
    html_tables = f"<h2>{device_type}</h2>"
    
    # Process each FOTA type (unisoc/bt)
    for fota_type, env_data in data.items():
        
        # Check if any environments exist for the current FOTA type
        if not any(env_data.values()):
            continue
        
        # make sure fota type is handeled
        if fota_type not in ['bt','unisoc']:
                raise Exception("Unhandeled fota type")
        
        # add fota type to html
        html_tables += f"<h3>{fota_type.upper()}</h3>"
        html_tables += "<table border='1'>"
        
        fws=[]
        
        # sum all fws , add only if passed min and avoid duplicates
        for env in env_data:
            for fw in env_data[env]:
                if fota_type == "unisoc" and int(env_data[env][fw]) >= unisoc_min:
                    if fw not in fws:
                        fws.append(fw)  
                    if fw not in unisoc_fota_status_dict:
                        unisoc_fota_status_dict[fw]= _get_target_unisoc_fw(fw)
                elif fota_type == "bt" and int(env_data[env][fw]) >= bt_min:
                    if fw not in fws:
                        fws.append(fw)
        
        # sort the fws
        if fota_type == "unisoc":
            sorted_fws= sorted(fws, key=lambda fw: tuple(map(int, fw[-13:].split('_'))))
        elif fota_type == "bt":
            sorted_fws= sorted(fws, key=lambda fw: (int(fw) if fw.isdigit() else -1))
        
        # Create table headers
        html_tables += "<tr><th> </th>"
        for fw_version in sorted_fws:
            html_tables += f"<th>{fw_version}</th>"
        html_tables += "</tr>"
        
        # Populate table rows
        for env in env_data:
            
            # if env have no active devices in it , skip it
            if not env_data[env].keys():
                continue
            
            # add each env and data accordingly
            html_tables += f"<tr><td>{env}</td>"
            for fw_version in sorted_fws:
                users_count = env_data[env].get(fw_version, "")
                try:
                    html_tables += f"<td>{'{:,}'.format(users_count)}</td>"
                except:
                    html_tables += "<td></td>"
            html_tables += "</tr>"
        
        # for each fw in the unisoc table , add it's target fw or N/A
        if fota_type == "unisoc":
            html_tables += "<tr><td>FOTA target (suggested network type)</td>"
            for fw_version in sorted_fws:
                try:
                    html_tables += f"<td>{unisoc_fota_status_dict[fw_version]}</td>"
                except:
                    html_tables += "<td></td>"
            html_tables += "</tr>"
        html_tables += "</table>"
    html_tables += "<br>"

    return html_tables

def get_html_section():
    # open html
    html='<html>'
    
    # add table css in head
    html+='<head><style>table {border-collapse: collapse;} table, th, td {border: 1px solid black; text-align: center;} th, td {padding: 8px;} th {background-color: #f2f2f2;} tr:nth-child(even) {background-color: #f2f2f2;} tr:nth-child(odd) {background-color: #fff;}</style></head>'
    
    # add body
    html+=f'<body><h4>Hi Everyone, this is the daily fw report :</h4><br>'
    
    for device_type in device_names_dict:
        # for each device get the html
        html += get_device_html_section(device_type,get_fw_dict(device_names_dict[device_type]))+"<br><br>"

    # close html
    html += f"<h4>All tables are built from devices that were active in Trackimo-prod, Watchinu-prod and MST during the last month.<br>*Unisoc fw's with less than {unisoc_min} devices are hidden (per env)<br>*BT fw's with less than {bt_min} devices are hidden (per env)</h4></body></html>"
    return html

def send_daily_report(debug:bool=False):
    try:
        html=get_html_section()
    except Exception as e:
        raise Exception("Failed to get html :",e)
    
    try:
        if debug:
            print(html)
            return
        send_email_using_amazon_ses(email_list,f'Daily FW Report {datetime.now().strftime("%d/%m/%Y")}',html=html,sender_name="FW Reporter")
    except:
        raise Exception("Failed to send email :",e)

if __name__ == "__main__":
    send_daily_report()