#!/bin/bash

run_single_python_script_for_shell() {
    local script_name="$1"
    local full_script_command="$2"

    if ps -ef | pgrep -f "python3 -u ${full_script_command}$" >/dev/null ; then
        echo "${script_name} script is already running.."
    else
        echo "Starting ${script_name} script"
        python3 -u ${full_script_command} >> "${shell_log}" 2>&1 &
        if [ $? -ne 0 ]; then
            echo "Failed to run ${script_name}"
        fi
    fi
}

# Validate logs folder
source 'shell_scripts/verify_log_folders.sh' || { exit 1; }
shell_log="logs/fw_reporter.log"

script_name="fw_report"
full_script_command="python_scripts/fota_scripts/general_fw_report.py"
run_single_python_script_for_shell "$script_name" "$full_script_command" >> $shell_log
wait
