#!/bin/bash

# independent consts
logs_folder='logs'

# Create logs folder if it doesn't exist
if [ ! -d "${logs_folder}" ]; then
    mkdir -p "$logs_folder"  # Use -p to avoid errors if the folder already exists
fi

# Create sub-folder if an argument is provided
if [ -n "$1" ]; then  # Use -n to check if a non-empty string is provided
    subfolder="${logs_folder}/${1}"
    if [ ! -d "${subfolder}" ]; then
        mkdir -p "${subfolder}"  # Use -p to create parent directories as needed
    fi
fi