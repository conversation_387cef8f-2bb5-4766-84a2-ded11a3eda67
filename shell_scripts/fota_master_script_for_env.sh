run_script_and_log_output() {
    # function vars
    local script_name="$1"
    local python_command="$2"
    local log_path="$log_folder/$script_name.log"

    # Initialize new_pid to -1
    new_pid=-1

    # Check if the script is already running , if so return initialized pid
    if ps -ef | grep -F "python3 -u ${python_command}" | grep -v grep >/dev/null ; then
        echo "${script_name} script is already running.." >> "$shell_log"
        return
    fi

    # Run the script
    echo "Starting ${script_name} script" >> "$shell_log"
    python3 -u ${python_command} >> "${log_path}" 2>&1 &

    # set new_pid
    new_pid=$!
}
handle_fota_script_results() {
    # function vars
    local script_name="$1"
    local exit_code="$2"
    local log_path="$log_folder/$script_name.log"

    # 0 = success
    # 1 = crash (exceptions)
    # 2 = no fota packages
    # 3 = no active devices
    if [ $exit_code -eq 1 ]; then
        echo "Failed to run ${script_name}" >> "${shell_log}"
    elif [ $exit_code -eq 2 ]; then
        echo "Couldn't find any FOTA packages available for ${script_name}" >> "${shell_log}"
        rm -f "${log_path}"
    elif [ $exit_code -eq 3 ]; then
        echo "Couldn't find active devices for ${script_name}" >> "${shell_log}"
    elif [ $exit_code -ne 0 ];then
        echo "Got unexpected exit code (${exit_code}) from ${script_name}, if it's not a mistake, make sure to add the status code to main shell script function 'handle_fota_script_results'">> "${shell_log}"
    fi
}
launch_general_fota_scripts_by_fota_type() {
    local fota_type="$1"

    # Declare an associative array to store PIDs by device_type
    declare -A pids

    # run for every device type
    for device_type in "${devices_types[@]}"; do
        # set vars 
        local script_name="general_${fota_type}_fota_for_${device_type}"
        local python_command="$general_script_path $env $fota_type $device_type"
        
        # run script by function
        run_script_and_log_output "$script_name" "$python_command"

        # if script started properly , add to the pids by device type
        if [ $new_pid -eq -1 ]; then
            continue
        fi
        pids["$device_type"]=$new_pid
    done

    # proccess each script results
    for device_type in "${!pids[@]}"; do
        # set vars
        local script_name="general_${fota_type}_fota_for_${device_type}"
        
        # wait for process to end
        local pid="${pids[$device_type]}"
        wait $pid
        
        # set status code and handle results by function
        local exit_code=$?
        handle_fota_script_results "$script_name" "$exit_code"
    done
}
launch_fota_scripts_from_list_by_fota_type() {
    local fota_type="$1"
    local script_name_format="${fota_type}_fota_from_list_for_${env}"
    local valid_scripts_names=()
    
    # check if there are any valid script names in scripts_folder
    for file in "$scripts_folder"/*; do
        # skip folders
        if [ ! -f "$file" ]; then
            continue
        fi

        # Get the file name (without the path)
        local file_name=$(basename "$file")

        # make sure file is python
        if [ "${file_name##*.}" != "py" ]; then
            continue
        fi

        # if matches current script_name_format , add the script name without extension to valid_scripts_names
        if [[ $file_name == "${script_name_format}"* ]]; then
            local script_name=${file_name%.*}
            valid_scripts_names+=("$script_name")
        fi
    done

    # if there are no scripts , return
    if [ ${#valid_scripts_names[@]} -eq 0 ]; then
        echo "Couldn't find ${fota_type} FOTA scripts from list for ${env}" >> "$shell_log"
        return
    fi

    # Declare an associative array to store PIDs by script_name 
    declare -A pids

    # start each valid script
    for script_name in "${valid_scripts_names[@]}"; do
        # python command == script_path
        local script_path="$scripts_folder/$script_name.py" 
        run_script_and_log_output "$script_name" "$script_path"
        
        # if script started properly , add to the pids by script_name
        if [ $new_pid -eq -1 ]; then
            continue
        fi
        pids["$script_name"]=$new_pid
    done 

    # proccess each script results
    for script_name in "${!pids[@]}"; do
        # wait for process to end
        local pid="${pids[$script_name]}"
        wait $pid
        
        # set status code and handle results by function
        local exit_code=$?
        handle_fota_script_results "$script_name" "$exit_code"
    done
}

# set consts
log_base_folder="logs"
scripts_folder="python_scripts/fota_scripts"
general_script_path="${scripts_folder}/general_fota.py"
get_device_types_script_path="${scripts_folder}/get_device_type_for_env.py"
if [ ! -f "$general_script_path" ]; then
    echo "general fota script isn't available" >&2
    exit 1
fi
if [ ! -f "$get_device_types_script_path" ]; then
    echo "get device types script isn't available" >&2
    exit 1
fi

# set user vars
env=$1

# Validate logs folder
source shell_scripts/verify_log_folders.sh "$env" || { exit 1;}

# set script vars
log_folder="${log_base_folder}/${env}"
shell_log="${log_folder}/master_script_shell.log"

# get device types
output=$(python3 -u ${get_device_types_script_path} ${env} 2>/dev/null)
exit_code=$?
if [ $exit_code -ne 0 ]; then
    echo "Error occurred while getting device types for ${env}" >&2
    exit 1
fi
devices_types=(${output})

# verify at least one device is available
if [ ${#devices_types[@]} -eq 0 ]; then
    echo "Couldn't find any device types for ${env}" >&2
    exit 1
fi

# run all scripts 
fota_types=(unisoc bt gps wifi)
for fota_type in "${fota_types[@]}"; do
    echo "-------------${fota_type} Scripts-------------" >> "$shell_log"
    launch_fota_scripts_from_list_by_fota_type "$fota_type"
    # Run general scripts only if env is exactly petloc8
    if [ "$env" != "trackimo_prod" ]; then
        launch_general_fota_scripts_by_fota_type "$fota_type"
    fi
done