#!/bin/bash

ensure_python_script_running() {
    local script_name="$1"
    local full_script_command="$2"
    local log_path="$log_folder/$script_name.log"

    if ps -ef | pgrep -f "python3 -u ${full_script_command}$" >/dev/null ; then
        echo "${script_name} script is already running.."
    else
        echo "Starting ${script_name} script"
        python3 -u ${full_script_command} >> "${log_path}" 2>&1 &
        if [ $? -ne 0 ]; then
            echo "Failed to run ${script_name}"
        fi
    fi
}

# Validate logs folder
source shell_scripts/verify_log_folders.sh "customized_scripts" || { echo "Failed to source verify_log_folders.sh"; exit 1; }

log_folder="logs/customized_scripts"
shell_log="$log_folder/shell.log"

echo "*******************" >> $shell_log
customized_scripts_path="python_scripts/customized_scripts"

# provide name for script and script file name
script_name=$1
script_file_name=$2

# set script file name in folder path
script_path="${customized_scripts_path}/${script_file_name}.py"
ensure_python_script_running "$script_file_name" "$script_path" >> $shell_log
wait
