
FOTA_ENVIRONMENTS = ['trackimo_prod', 'trackipet', 'mst', 'petloc8']
FOTA_TYPES = ['unisoc', 'bt', 'gps', 'wifi']

DEVICE_TYPE_ID_DICT = {
    "Universal": 0,
    "Slim": 5,
    'Curve4G': 16,
    "TrackiPro4g": 17,
    "PetLoc8": 23,
    "MiniCat1": 25,
    "Loc8Universal": 28
}

# Default filter values
DEFAULT_FILTERS = {
    "battery": {
        "min_percentage": 30,
        "max_age_minutes": 60
    },
    "fota_history": {
        "max_attempts_today": 5,
        "max_total_attempts": 30,
        "retry_after_hours": 24
    }
}

# Job execution limits
DEFAULT_JOB_LIMITS = {
    "max_devices_per_run": 50,
    "min_devices_for_execution": 3,
    "max_daily_attempts": 5
}

# Database refresh rates
DB_QUERY_REFRESH_RATE = 5  # seconds between database updates

# Logging configuration
LOG_LEVELS = {
    "DEBUG": 10,
    "INFO": 20,
    "WARNING": 30,
    "ERROR": 40,
    "CRITICAL": 50
}


# Docomo operators (from your existing code)
DOCOMO_OPERATORS = ['KDDI', 'Docomo']  # Add other Docomo operators as needed
