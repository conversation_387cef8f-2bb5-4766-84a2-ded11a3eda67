from trackimo_package.simple_tools.fota import docomo_operators,send_unisoc_fota,send_bt_fota,send_wifi_fota,send_gps_fota
from trackimo_package.servers.message_post import (
    send_ota_start_message
)
def send_fota_and_update_device(env,fota_type,device):
    if fota_type == 'unisoc':
        send_unisoc_fota(env,device.get('id'))
    elif fota_type == "bt" :
        # set fota link
        fota_link = device.get('fota_link','')
        send_bt_fota(env,device.get('id'),fota_link)
    elif fota_type == 'wifi':
        send_ota_start_message(env,device.get('id'),fw_version='D')
    elif fota_type == 'gps':
        send_gps_fota(env,device.get('id'))
    else:
        raise Exception("Unknown fota type provided : " + fota_type)
    return device



