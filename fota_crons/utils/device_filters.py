#!/usr/bin/env python3
"""
Device Filter Processing Logic
Handles JSON-based device selection criteria for FOTA jobs
"""

import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from trackimo_package.servers.mysql import _send_query_and_get_results, mysql_general_decorator


class DeviceFilterProcessor:
    """Processes custom filters for device selection"""

    def __init__(self, env: str):
        self.env = env
        self.db_env = self._get_db_env(env)

    def _get_db_env(self, env: str) -> str:
        """Convert environment name to database environment"""
        if env == "watchinu_prod":
            return "watchinu_prod_trackimo"
        return env

    def apply_filters(self, devices: List[Dict], filters: Dict[str, Any]) -> List[Dict]:

        if not filters:
            return devices

        filtered_devices = []

        for device in devices:
            if self._device_passes_filters(device, filters):
                filtered_devices.append(device)

        return filtered_devices

    def _device_passes_filters(self, device: Dict, filters: Dict[str, Any]) -> bool:
        """Check if a single device passes all filters"""
        if 'device_ids' in filters:
            if not self._check_device_ids_filter(device, filters['device_ids']):
                return False
        if 'power_saving_mode' in filters:
            if not self._check_power_saving_mode_filter(device, filters['power_saving_mode']):
                return False
        # FOTA history filters
        if 'fota_history' in filters:
            if not self._check_fota_history_filter(device, filters['fota_history']):
                return False
        return True

    def _check_power_saving_mode_filter(self, device: Dict, power_saving_mode_filter: bool) -> bool:
        """Check if device is in power saving mode"""
        device_id = device.get('id')
        print(f"[POWER_SAVING_FILTER] Checking device {device_id}, filter expects power_saving_mode={power_saving_mode_filter}")

        if not device_id:
            print(f"[POWER_SAVING_FILTER] Device ID not found")
            device['filter_reason'] = "Device ID not found"
            return False

        try:
            # Condition 1: Check devices_states table - gsm_sleep_mode_enabled must be 0 (false)
            # Entry must be present
            devices_states_query = f"""
                SELECT gsm_sleep_mode_enabled
                FROM trackimo.devices_states
                WHERE device_id = {device_id}
            """
            print(f"[POWER_SAVING_FILTER] Checking devices_states for device {device_id}")

            devices_states_result = _send_query_and_get_results(self.db_env, devices_states_query)
            if not devices_states_result:
                print(f"[POWER_SAVING_FILTER] Device {device_id} not found in devices_states table")
                device['filter_reason'] = "Device not found in devices_states table"
                return False

            gsm_sleep_mode_enabled = devices_states_result[0]['gsm_sleep_mode_enabled']
            print(f"[POWER_SAVING_FILTER] Device {device_id} gsm_sleep_mode_enabled: {gsm_sleep_mode_enabled}")
            if gsm_sleep_mode_enabled != 0:
                print(f"[POWER_SAVING_FILTER] Device {device_id} FILTERED: GSM sleep mode enabled")
                device['filter_reason'] = f"GSM sleep mode enabled: {gsm_sleep_mode_enabled}"
                return False

            # Condition 2: Check bt_communication_setup table - pairing_enabled must be 0 (false)
            # Entry is optional - if not present, device is not in power saving mode
            bt_communication_query = f"""
                SELECT pairing_enabled
                FROM trackimo.bt_communication_setup
                WHERE device_id = {device_id}
            """
            print(f"[POWER_SAVING_FILTER] Checking bt_communication_setup for device {device_id}")

            bt_communication_result = _send_query_and_get_results(self.db_env, bt_communication_query)
            if bt_communication_result:
                pairing_enabled = bt_communication_result[0]['pairing_enabled']
                print(f"[POWER_SAVING_FILTER] Device {device_id} pairing_enabled: {pairing_enabled}")
                if pairing_enabled != 0:
                    print(f"[POWER_SAVING_FILTER] Device {device_id} FILTERED: BT pairing enabled")
                    device['filter_reason'] = f"BT pairing enabled: {pairing_enabled}"
                    return False
            else:
                print(f"[POWER_SAVING_FILTER] Device {device_id} not found in bt_communication_setup (optional table)")

            # Condition 3: Check unisoc_bluetooth table - ble_mode must be 1
            # Entry is optional - if not present, device is not in power saving mode
            unisoc_bluetooth_query = f"""
                SELECT ble_mode
                FROM trackimo.unisoc_bluetooth
                WHERE devices_states_device_id = {device_id}
            """
            print(f"[POWER_SAVING_FILTER] Checking unisoc_bluetooth for device {device_id}")

            unisoc_bluetooth_result = _send_query_and_get_results(self.db_env, unisoc_bluetooth_query)
            if unisoc_bluetooth_result:
                ble_mode = unisoc_bluetooth_result[0]['ble_mode']
                print(f"[POWER_SAVING_FILTER] Device {device_id} ble_mode: {ble_mode}")
                if ble_mode != 1:
                    print(f"[POWER_SAVING_FILTER] Device {device_id} FILTERED: BLE mode not 1")
                    device['filter_reason'] = f"BLE mode not 1: {ble_mode}"
                    return False
            else:
                print(f"[POWER_SAVING_FILTER] Device {device_id} not found in unisoc_bluetooth (optional table)")

            # If we reach here, device meets all power saving mode conditions
            print(f"[POWER_SAVING_FILTER] Device {device_id} meets all power saving mode conditions")
            print(f"[POWER_SAVING_FILTER] Device {device_id} PASSED power saving mode filter")
            return True

        except Exception as e:
            print(f"[POWER_SAVING_FILTER] Device {device_id} ERROR: {e}")
            device['filter_reason'] = f"Power saving mode check error: {e}"
            return False


    def _check_device_ids_filter(self, device: Dict, device_ids_filter: Optional[List[int]]) -> bool:
        if not device_ids_filter:
            return True
        device_id = device.get('id')
        if device_id not in device_ids_filter:
            device['filter_reason'] = f"Device ID {device_id} not in filter list"
            return False
        return True


    def _check_fota_history_filter(self, device: Dict, fota_filter: Dict) -> bool:
        """Check FOTA history filters"""
        max_attempts_today = fota_filter.get('max_attempts_today', 5)
        device_id = device.get('id')
        # Query to count OTA attempts (msg_id = 36) in last 24 hours
        query = f"""
            SELECT COUNT(*) AS attempt_count
            FROM audit_db.gps_raw_log
            WHERE device_id = {device_id}
            AND msg_id = 36
            AND timestamp >= NOW() - INTERVAL 24 HOUR
        """
        try:
            result = _send_query_and_get_results("trackimo_dev",query, self.env)
            attempt_count = result[0]['attempt_count'] if result else 0
        except Exception as e:
            device['filter_reason'] = f"FOTA check error: {e}"
            return False

        if attempt_count >= max_attempts_today:
            device['filter_reason'] = f"FOTA attempts in last 24h: {attempt_count}"
            return False

        return True


def apply_custom_filters(devices: List[Dict], filters: Dict[str, Any], env: str) -> List[Dict]:
    processor = DeviceFilterProcessor(env)
    return processor.apply_filters(devices, filters)


