import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# Note: croniter needs to be installed: pip install croniter
try:
    from croniter import croniter
except ImportError:
    print("Warning: croniter not installed. Install with: pip install croniter")
    croniter = None
from trackimo_package.servers.mysql import _send_query_and_get_results, _send_query_and_commit, mysql_general_decorator


@mysql_general_decorator()
def get_active_fota_jobs(env: str = None) -> List[Dict]:
    query = f"""
    SELECT 
        id, name, description, env, device_type_id, fota_type,
        source_version, destination_version, unisoc_version, bt_version, fota_link, custom_filters, cron_schedule,
        max_devices_per_run, min_devices_for_execution, last_run, next_run
    FROM fota_service.fota_jobs 
    WHERE is_active = 1 
        AND is_valid = 1
    """

    return _send_query_and_get_results(env, query)


def get_devices_for_job(job: Dict, device_ids: Optional[List[int]] = None, last_active: int = 60, min_battery: int = 30) -> List[Dict]:
    env = job['env']
    device_type_id = job['device_type_id']
    fota_type = job['fota_type']
    source_version = job['source_version']
    destination_version = job['destination_version']
    unisoc_version = job['unisoc_version']
    bt_version = job['bt_version']
    base_query = _build_device_query(
        fota_type,
        device_type_id,
        source_version,
        destination_version,
        unisoc_version,
        bt_version,
        device_ids=device_ids,
        last_active=last_active,
        min_battery=min_battery,
    )

    raw_results = _send_query_and_get_results(env=env, full_query=base_query, to_print=False)

    DEVICE_FIELDS = ['id', 'last_active', 'last_battery_update', 'current_fw']
    all_devices = [dict(zip(DEVICE_FIELDS, row)) for row in raw_results]

    return all_devices



def _get_db_env(env: str) -> str:
    """Convert environment name to database environment"""
    if env == "watchinu_prod":
        return "watchinu_prod_trackimo"
    return env


def _build_device_query(
    fota_type: str,
    device_type_id: int,
    source_version: str,
    destination_version: str,
    unisoc_version: str,
    bt_version: str,
    device_ids: Optional[List[int]] = None,
    last_active: int = 60,
    min_battery: int = 30,
) -> str:
    """Build the base device selection query with latest location and optional device filtering."""

    # Base joins with only latest last_location record
    base_tables = """
    FROM trackimo.devices d
    INNER JOIN trackimo.devices_states ds ON ds.device_id = d.id
    INNER join trackimo.device_last_shutdown dls on dls.device_id = d.id
    INNER JOIN trackimo.devices_last_location dll ON dll.device_id = d.id
    LEFT JOIN trackimo.devices_last_battery dlb ON dlb.device_id = d.id
    """

    additional_tables = ""
    fw_condition = ""
    fw_select = ""

    if fota_type == 'unisoc':
        fw_condition = f"ds.fw_version LIKE '%{source_version}%'"
        fw_select = "ds.fw_version AS current_fw"

    elif fota_type == 'bt':
        additional_tables = """
        INNER JOIN trackimo.unisoc_bluetooth ub ON d.id = ub.devices_states_device_id
        """
        fw_condition = (
            f"ds.fw_version LIKE '%{unisoc_version}%' "
            f"AND ub.fw_version NOT LIKE '%{destination_version}%'"
        )
        fw_select = "ub.fw_version AS current_fw"

    elif fota_type == 'wifi':
        additional_tables = """
        INNER JOIN trackimo.unisoc_bluetooth ub ON d.id = ub.devices_states_device_id
        INNER JOIN trackimo.device_additional_details dad ON d.id = dad.device_id
        """
        fw_condition = (
            f"ds.fw_version LIKE '%{unisoc_version}%'"
            f"AND dad.wifiFWVersion NOT LIKE '%{destination_version}%'"
            f"AND ub.fw_version LIKE '%{bt_version}%'"
        )
        fw_select = "dad.wifiFWVersion AS current_fw"

    elif fota_type == 'gps':
        additional_tables = """
        INNER JOIN trackimo.device_additional_details dad ON d.id = dad.device_id
        """
        fw_condition = (
            f"dad.gpsFWVersion NOT LIKE '%{source_version}%' "
            f"AND dad.gpsFWVersion NOT IN ('', ' ')"
        )
        fw_select = "dad.gpsFWVersion AS current_fw"

    activity_condition = get_general_active_device_condition(last_active, min_battery)

    # ✅ WHERE clause
    where_clause = f"WHERE ({fw_condition}) AND {activity_condition}"

    if device_ids:
        id_list = ', '.join(map(str, device_ids))
        where_clause += f" AND d.id IN ({id_list})"

    # SELECT fields
    select_fields = f"""
    SELECT
        d.id,
        dll.updated AS last_active,
        dll.battery AS last_battery_update,
        {fw_select}
    """


    # Final query
    query = f"""
    {select_fields}
    {base_tables}
    {additional_tables}
    {where_clause}
    GROUP BY d.id
    """

    return query

def get_general_active_device_condition(last_active:int,min_battery:int=30):
    return f"((dll.updated >= (NOW() - INTERVAL {last_active} MINUTE) AND dll.battery > {min_battery} AND dll.updated > dls.updated) OR (dlb.updated >= (NOW() - INTERVAL {last_active} MINUTE) AND dlb.battery > {min_battery} AND dlb.updated > dls.updated))"



def update_job_run_times(job_id: int, last_run: datetime, next_run: datetime):
    """Update last_run and next_run for a specific FOTA job"""

    # Convert datetimes to SQL string format
    last_run_str = last_run.strftime('%Y-%m-%d %H:%M:%S')
    next_run_str = next_run.strftime('%Y-%m-%d %H:%M:%S')

    # Build full SQL query
    full_query = f"""
        UPDATE fota_service.fota_jobs
        SET last_run = '{last_run_str}', 
            next_run = '{next_run_str}', 
            updated_at = NOW()
        WHERE id = {job_id};
    """
    _send_query_and_commit(env="trackimo_prod", full_query=full_query)

def _getBTQueryForPetloc8(job: Dict) -> List[Dict]:
    env = job['env']
    source_version = job['source_version']
    destination_version = job['destination_version']
    max_devices_per_run = job['max_devices_per_run']
    full_query = f"""
    SELECT DISTINCT
        d.id,
        dll.updated AS last_active,
        dll.battery AS last_battery_update,
        ub.fw_version AS current_fw 
    FROM trackimo.devices d
    INNER JOIN trackimo.devices_states ds ON ds.device_id = d.id
    INNER JOIN trackimo.devices_last_location dll ON dll.device_id = d.id
    LEFT JOIN trackimo.devices_last_battery dlb ON dlb.device_id = d.id
    INNER JOIN trackimo.unisoc_bluetooth ub ON d.id = ub.devices_states_device_id
    WHERE ub.fw_version LIKE '%{source_version}%'
        AND ub.fw_version NOT LIKE '%{destination_version}%'
        AND ds.fw_version LIKE '%20250519_1607%'
    ORDER BY dll.updated DESC
    LIMIT {max_devices_per_run}
    """

    raw_results = _send_query_and_get_results(env=env, full_query=full_query, to_print=False)

    # Convert to list of dicts
    DEVICE_FIELDS = ['id', 'last_active', 'last_battery_update', 'current_fw']
    all_devices = [dict(zip(DEVICE_FIELDS, row)) for row in raw_results]

    return all_devices

def _getWifiQueryForPetloc8(job: Dict) -> List[Dict]:
    env = job['env']
    source_version = job['source_version']
    max_devices_per_run = job['max_devices_per_run']
    full_query = f"""
    SELECT DISTINCT
        d.id,
        dll.updated AS last_active,
        dll.battery AS last_battery_update,
        dad.wifiFWVersion AS current_fw
    FROM trackimo.devices d
    INNER JOIN trackimo.devices_states ds ON ds.device_id = d.id
    INNER JOIN trackimo.devices_last_location dll ON dll.device_id = d.id
    LEFT JOIN trackimo.devices_last_battery dlb ON dlb.device_id = d.id
    INNER JOIN trackimo.unisoc_bluetooth ub ON d.id = ub.devices_states_device_id
    INNER JOIN trackimo.device_additional_details dad ON d.id = dad.device_id
    WHERE (dad.wifiFWVersion NOT LIKE '%{source_version}%')
        AND dad.wifiFWVersion IS NOT NULL
        AND ds.fw_version LIKE '%20250519_1607%'
        AND ub.fw_version LIKE '%CAT1_BT_V007_20250517%'
    ORDER BY dll.updated DESC
    LIMIT {max_devices_per_run}
    """

    raw_results = _send_query_and_get_results(env=env, full_query=full_query, to_print=False)

    # Convert to list of dicts
    DEVICE_FIELDS = ['id', 'last_active', 'last_battery_update', 'current_fw']
    all_devices = [dict(zip(DEVICE_FIELDS, row)) for row in raw_results]

    return all_devices


def calculate_next_run(cron_schedule: str, base_time: datetime = None) -> datetime:

    if base_time is None:
        base_time = datetime.now()

    if croniter is None:
        # Fallback if croniter not available: run again in 1 hour
        return base_time + timedelta(hours=1)

    try:
        cron = croniter(cron_schedule, base_time)
        return cron.get_next(datetime)
    except Exception as e:
        # Fallback: run again in 1 hour
        return base_time + timedelta(hours=1)


