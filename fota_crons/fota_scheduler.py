import sys
from datetime import datetime, timezone
from typing import List, Dict
from fota_crons.utils.db_service import get_active_fota_jobs, update_job_run_times
from .fota_job_executor import execute_fota_job
from croniter import croniter
from dateutil import parser as date_parser

class FOTAScheduler:
    """Main FOTA scheduler class"""
    def __init__(self, env: str = None):
        self.env = env
        self.start_time = datetime.now()
        self.jobs_executed = 0
        self.jobs_failed = 0

    def run(self):
        """Main scheduler execution"""
        print(f"FOTA Scheduler started at {self.start_time}")

        try:
            # Get jobs that should run now
            jobs_to_run = self._get_scheduled_jobs()

            if not jobs_to_run:
                print("No jobs scheduled to run at this time")
                return

            print(f"Found {len(jobs_to_run)} jobs to execute")

            for job in jobs_to_run:
                self._execute_job(job)

        except Exception as e:
            print(f"FATAL ERROR in scheduler: {str(e)}")
            sys.exit(1)

    def _get_scheduled_jobs(self) -> List[Dict]:
        """Get jobs that should run now based on cron_schedule and last_run"""
        try:
            raw_jobs = get_active_fota_jobs(env=self.env)
            now = datetime.now(timezone.utc)
            due_jobs = []

            FOTA_JOB_FIELDS = [
                'id', 'name', 'description', 'env', 'device_type_id', 'fota_type',
                'source_version', 'destination_version', 'unisoc_version', 'bt_version', 'fota_link', 'custom_filters',
                'cron_schedule', 'max_devices_per_run', 'min_devices_for_execution',
                'last_run', 'next_run'
            ]

            for job_tuple in raw_jobs:
                job = dict(zip(FOTA_JOB_FIELDS, job_tuple))
                cron_expr = job.get('cron_schedule')
                last_run = job.get('last_run')

                # Parse string to datetime if needed
                if isinstance(last_run, str):
                    last_run = date_parser.parse(last_run)

                # Ensure last_run is timezone-aware
                if last_run and last_run.tzinfo is None:
                    last_run = last_run.replace(tzinfo=timezone.utc)

                # First-time job? Schedule immediately
                if last_run is None:
                    print(f"→ First-time run for job '{job['name']}', scheduling now")
                    job['next_run'] = now
                    due_jobs.append(job)
                    continue

                try:
                    cron = croniter(cron_expr, last_run)
                    next_run = cron.get_next(datetime)

                    # Ensure next_run is timezone-aware
                    if next_run.tzinfo is None:
                        next_run = next_run.replace(tzinfo=timezone.utc)

                    if now >= next_run and (now - next_run).total_seconds() < 60:
                        job['next_run'] = next_run
                        due_jobs.append(job)

                except Exception as e:
                    print(f"⚠ Invalid cron expression for job {job['id']}: {cron_expr} ({str(e)})")

            return due_jobs

        except Exception as e:
            print(f"ERROR: Failed to get scheduled jobs: {str(e)}")
            return []

    def _execute_job(self, job: Dict):
        job_name = job.get('name', 'Unknown')
        try:
            # Update job run times before execution
            now = datetime.now(timezone.utc)
            cron = croniter(job['cron_schedule'], now)
            next_run = cron.get_next(datetime)
            update_job_run_times(job.get('id'), now, next_run)

            # Execute the job
            result = execute_fota_job(job)

            if result.get('success', False):
                print(f"✓ Job '{job_name}' completed successfully")
                self.jobs_executed += 1
            else:
                print(f"✗ Job '{job_name}' failed: {result.get('error', 'Unknown error')}")
                self.jobs_failed += 1

        except SystemExit as e:
            # Handle exit codes from job executor
            exit_code = e.code
            if exit_code == 2:
                print(f"⚠ Job '{job_name}' skipped: No FOTA packages available")
            elif exit_code == 3:
                print(f"⚠ Job '{job_name}' skipped: No active devices found")
            else:
                print(f"✗ Job '{job_name}' failed with exit code {exit_code}")
                self.jobs_failed += 1

        except Exception as e:
            print(f"✗ Job '{job_name}' failed with exception: {str(e)}")
            self.jobs_failed += 1


def main():
    env = "trackimo_prod"
    scheduler = FOTAScheduler(env)
    scheduler.run()

if __name__ == "__main__":
    main()