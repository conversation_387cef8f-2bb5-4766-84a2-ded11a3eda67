import json
import sys
import time
from datetime import datetime
from typing import Dict, List, Any
from fota_crons.utils.db_service import (
    get_devices_for_job
)
from fota_crons.utils import db_service
from fota_crons.utils.device_filters import apply_custom_filters
from fota_crons.utils.config import DEFAULT_JOB_LIMITS, DB_QUERY_REFRESH_RATE
from fota_crons.utils.fota_messages import send_fota_and_update_device
from fota_crons.utils.utils import update_devices_fw_version

class FOTAJobExecutor:
    """Executes individual FOTA jobs"""

    def __init__(self, job: Dict):
        self.job = job
        self.job_id = job['id']
        self.env = job['env']
        self.fota_type = job['fota_type']
        self.results = {
            'job_id': self.job_id,
            'job_name': job['name'],
            'start_time': datetime.now(),
            'devices_processed': 0,
            'devices_updated': 0,
            'devices_failed': 0,
            'errors': [],
            'warnings': []
        }

    def _finish_with_error(self, error: Exception):
        print(f"Job failed with error: {error}")
        # Optionally: update job status in DB, notify system, etc.

    def execute(self) -> Dict[str, Any]:
        try:
            print(f"Starting FOTA job: {self.job['name']} (ID: {self.job_id})")

            # Step 1: Get candidate devices
            devices = self._get_candidate_devices()
            if not devices:
                return self._finish_successfully(message="No candidate devices found")

            # Step 2: Apply custom filters
            filtered_devices = self._apply_filters(devices)

            updated_devices = update_devices_fw_version(
                env=self.job['env'],
                fota_type=self.job['fota_type'],
                devices=filtered_devices,
                is_after_fota_message=False
            )

            # Step 3: Execute FOTA updates
            self._execute_fota_updates(updated_devices)

            updated_devices = update_devices_fw_version(
                env=self.env,
                fota_type=self.job['fota_type'],
                devices=filtered_devices,
                is_after_fota_message=True
            )

            return self._finish_successfully()

        except Exception as e:
            return self._finish_with_error(f"Job execution failed: {str(e)}")

    def _finish_successfully(self, message: str = "Job completed successfully") -> Dict[str, Any]:
        self.results['end_time'] = datetime.now()
        self.results['duration'] = (self.results['end_time'] - self.results['start_time']).total_seconds()
        self.results['success'] = True
        self.results['message'] = message

        print(message)
        print(f"  - Devices processed: {self.results.get('devices_processed', 0)}")
        print(f"  - Devices updated: {self.results.get('devices_updated', 0)}")

        return self.results

    def _get_candidate_devices(self) -> List[Dict]:
        """Get candidate devices for the job"""
        try:
            method_name = None
            device_ids = None
            last_active = None
            min_battery = None
            if self.job.get('custom_filters'):
                try:
                    filters = json.loads(self.job['custom_filters'])
                    device_ids = filters.get('device_ids')
                    method_name = filters.get('method')
                    last_active = filters.get('last_active', 60)
                    min_battery = filters.get('min_battery', 30)
                except Exception as e:
                    print(f"Warning: Failed to parse custom_filters JSON: {e}")
            if method_name:
                print("In custom method to fetch devices")
                # Dynamically call the custom method from db_service
                if not hasattr(db_service, method_name):
                    raise Exception(f"Custom device fetch method '{method_name}' not found in db_service.")
                method = getattr(db_service, method_name)
                devices = method(self.job)
                print(f"Used custom method '{method_name}' from db_service to get devices")
            else:
                devices = get_devices_for_job(self.job, device_ids=device_ids, last_active=last_active, min_battery=min_battery)
                print(f"Used default method to get devices")

            for device in devices:
                device['job_id'] = self.job['id']
                device['target_version'] = self.job['destination_version']
                device['fota_type'] = self.job['fota_type']
                device['fota_link'] = self.job['fota_link']
                device['env'] = self.job['env']
            return devices
        except Exception as e:
            raise Exception(f"Failed to get candidate devices: {str(e)}")

    def _apply_filters(self, devices: List[Dict]) -> List[Dict]:
        """Apply custom filters to devices"""
        if not self.job.get('custom_filters'):
            return devices

        try:
            filters = json.loads(self.job['custom_filters'])
            filtered_devices = apply_custom_filters(devices, filters, self.env)
            max_per_run = self.job.get('max_devices_per_run')
            if max_per_run is not None:
                try:
                    max_per_run = int(max_per_run)
                    if max_per_run > 0:
                        filtered_devices = filtered_devices[:max_per_run]
                        print(f"Limiting output to {max_per_run} devices (max_devices_per_run)")
                except (ValueError, TypeError):
                    self.results['warnings'].append(
                        f"Invalid value for max_devices_per_run: {self.job['max_devices_per_run']}"
                    )

            # Log filter results
            filtered_count = len(filtered_devices)
            rejected_count = len(devices) - filtered_count

            print(f"Filters applied: {filtered_count} devices passed, {rejected_count} rejected")

            return filtered_devices

        except Exception as e:
            self.results['warnings'].append(f"Filter application failed: {str(e)}")
            return devices

    def _validate_device_count(self, devices: List[Dict]) -> bool:
        """Validate that device count is within acceptable limits"""
        device_count = len(devices)
        max_devices = self.job.get('max_devices_per_run', DEFAULT_JOB_LIMITS['max_devices_per_run'])

        if device_count > max_devices:
            print(f"Too many devices, limiting to {max_devices}")
            # Truncate device list
            devices[:] = devices[:max_devices]

        return True

    def _execute_fota_updates(self, devices: List[Dict]):
        """Execute FOTA updates for all devices"""
        print(f"Executing FOTA updates for {len(devices)} devices")

        for i, device in enumerate(devices, 1):
            try:
                result = send_fota_and_update_device(device.get('env'), device.get('fota_type'), device)
                # Rate limiting
                time.sleep(DB_QUERY_REFRESH_RATE)

            except Exception as e:
                error_msg = f"Failed to process device {device['id']}: {str(e)}"
                print(f"ERROR: {error_msg}")
                self.results['errors'].append({
                    'device_id': device['id'],
                    'error': error_msg
                })
                self.results['devices_failed'] += 1


def execute_fota_job(job: Dict) -> Dict[str, Any]:

    executor = FOTAJobExecutor(job)
    return executor.execute()

