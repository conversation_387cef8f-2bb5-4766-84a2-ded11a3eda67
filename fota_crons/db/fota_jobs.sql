CREATE TABLE `fota_jobs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'Human-readable job name',
  `description` text COMMENT 'Job description/purpose',
  `env` enum('trackimo_prod','trackipet','mst','petloc8','trackimo_dev') NOT NULL,
  `device_type_id` tinyint NOT NULL COMMENT 'References trackimo.device_types.id',
  `fota_type` enum('unisoc','bt','wifi','gps') NOT NULL,
  `source_version` varchar(50) NOT NULL COMMENT 'Current firmware version pattern',
  `destination_version` varchar(50) NOT NULL COMMENT 'Target firmware version',
  `unisoc_version` varchar(50) DEFAULT NULL COMMENT 'Unisoc firmware version',
  `bt_version` varchar(50) DEFAULT NULL COMMENT 'Bluetooth firmware version',
  `fota_link` varchar(200) DEFAULT NULL COMMENT 'Direct firmware download link',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Job is enabled',
  `is_valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Job is valid for execution',
  `custom_filters` json DEFAULT NULL COMMENT 'Device selection criteria',
  `cron_schedule` varchar(50) DEFAULT NULL COMMENT 'Cron expression for scheduling',
  `last_run` timestamp NULL DEFAULT NULL,
  `next_run` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` varchar(50) DEFAULT NULL,
  `max_devices_per_run` int DEFAULT NULL COMMENT 'Max number of devices to process per run',
  `min_devices_for_execution` int DEFAULT NULL COMMENT 'Min number of devices required to execute the job',
  PRIMARY KEY (`id`),
  UNIQUE KEY `fota_jobs_unique` (`env`,`device_type_id`,`fota_type`,`source_version`),
  KEY `fota_jobs_env_active` (`env`,`is_active`),
  KEY `fota_jobs_schedule` (`cron_schedule`,`next_run`),
  KEY `fota_jobs_device_types_FK` (`device_type_id`),
  CONSTRAINT `fota_jobs_device_types_FK` FOREIGN KEY (`device_type_id`) REFERENCES `trackimo`.`device_types` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
